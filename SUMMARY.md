# Summary

[Introduction](README.md)

# User Guide

- [Installation](docs/user/installation.md)
- [Use Cases](docs/user/use-cases.md)
- [User Interface](docs/user/ui.md)
- [Users/Groups](docs/user/users-groups.md)
- [Notifications](docs/user/notifications.md)
- [Clients](docs/user/clients.md)
  - [CAN bus](docs/user/can.md)
  - [File](docs/user/file.md)
  - [Database](docs/user/database.md)
  - [Modbus](docs/user/modbus.md)
  - [1-Wire](docs/user/onewire.md)
  - [Messaging services](docs/user/messaging.md)
  - [MCU Devices](docs/user/mcu.md)
  - [Metrics](docs/user/metrics.md)
  - [Particle.io](docs/user/particle.md)
  - [Rules](docs/user/rules.md)
  - [<PERSON>y IoT](docs/user/shelly.md)
  - [Signal Generator](docs/user/signal-generator.md)
  - [Synchronization](docs/user/sync.md)
  - [Update](docs/user/update.md)
  - [USB](docs/user/usb.md)
- [Graphing](docs/user/graphing.md)
- [Configuration](docs/user/configuration.md)
- [Status/Errata](docs/user/status.md)
- [FAQ](docs/user/faq.md)

# Reference Guide

- [Documentation](docs/ref/documentation.md)
- [Vision](docs/ref/vision.md)
- [Architecture](docs/ref/architecture.md)
  - [System](docs/ref/architecture-system.md)
  - [Application](docs/ref/architecture-app.md)
  - [Client](docs/ref/client.md)
- [Development](docs/ref/development.md)
- [Data](docs/ref/data.md)
  - [Store](docs/ref/store.md)
  - [Synchronization](docs/ref/sync.md)
- [Reliability](docs/ref/reliability.md)
- [API](docs/ref/api.md)
- [Frontend](docs/ref/frontend.md)
- [Rules](docs/ref/rules.md)
- [Notifications](docs/ref/notifications.md)
- [Integration](docs/ref/integration.md)
- [Serial Devices](docs/ref/serial.md)
- [Version](docs/ref/version.md)
- [Security](docs/ref/security.md)
- [Research](docs/ref/research.md)

# Architecture Decision Records

- [Overview](docs/adr/README.md)
- [ADR-1: Point data type](docs/adr/1-consider-changing-point-data-type.md)
- [ADR-2: Authz](docs/adr/2-authz.md)
- [ADR-3: Node life cycle](docs/adr/3-node-lifecycle.md)
- [ADR-4: Time storage/format considerations](docs/adr/4-time.md)
- [ADR-5: Time validation](docs/adr/5-time-validation.md)
- [ADR-6: Time storage in rule schedule](docs/adr/6-time-storage-in-rule-schedule.md)
- [ADR-7: Jetstream store](docs/adr/7-jetstream-store.md)
- [ADR Template](docs/adr/template.md)

{"name": "simpleiot-js", "version": "1.1.0", "description": "SimpleIOT JavaScript API using NATS / WebSockets", "main": "siot-nats.mjs", "scripts": {"test": "mocha", "format": "prettier --ignore-path ../.gitignore --write \"./*.{mjs,js}\"", "lint": "eslint ./*.{mjs,js}"}, "author": "<PERSON> <<EMAIL>> (http://blakeminer.com)", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/simpleiot/simpleiot.git"}, "homepage": "https://github.com/simpleiot/simpleiot/tree/master/frontend/lib#readme", "dependencies": {"google-protobuf": "^3.20.1", "nats.ws": "^1.7.2"}, "devDependencies": {"mocha": "^10.1.0", "ws": "^8.11.0"}}
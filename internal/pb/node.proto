syntax = "proto3";
package pb;

option go_package = "internal/pb";

import "point.proto";

// Maps to NodeEdge type in data/node.go
message Node {
    string id = 1;
    string type = 2;
    int32 hash = 4;
    // bool tombstone = 5; // DEPRECATED: Tombstone is now an edge point
    string parent = 6;
    repeated Point points = 3;
    repeated Point edgePoints = 7;
}

message NodeRequest {
  Node node = 1;
  string error = 2;
}

message Nodes {
  repeated Node nodes = 1;
}

message NodesRequest {
  repeated Node nodes = 1;
  string error = 2;
}

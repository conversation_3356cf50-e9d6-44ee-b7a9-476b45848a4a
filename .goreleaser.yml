# This is an example goreleaser.yaml file with some sane defaults.
# Make sure to check the documentation at http://goreleaser.com
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod download
    # you may remove this if you don't need go generate
    - go generate ./...
changelog:
  skip: true
builds:
  - main: ./cmd/siot/main.go
    id: siot
    binary: siot
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - darwin
      - windows
    goarch:
      - amd64
      - arm
      - arm64
      - riscv64
      - 386
    goarm:
      - 6
      - 7
    ignore:
      - goos: darwin
        goarch: 386
      - goos: darwin
        goarch: riscv64
      - goos: windows
        goarch: arm
      - goos: windows
        goarch: riscv64
    hooks:
      pre:
        #- /bin/sh -c '. ./envsetup.sh && siot_setup && siot_build_dependencies'
archives:
  - name_template: >-
      {{- .ProjectName }}-
      {{- .Tag }}-
      {{- if eq .Os "darwin" }}macos
      {{- else }}{{ .Os }}{{ end }}-
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}{{ .Arm }}{{ end }}
    wrap_in_directory: true
    format: tar.gz
    format_overrides:
      - goos: windows
        format: zip
    files:
      - README.md
      - LICENSE
      - CHANGELOG.md


checksum:
  name_template: "checksums.txt"
snapshot:
  name_template: "{{ .Tag }}-next"
#env_files:
#github_token: GITHUB_TOKEN

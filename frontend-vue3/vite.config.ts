import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        additionalData: `@import "@/styles/variables.less";`
      }
    }
  },
  server: {
    port: 3001,  // 明确使用3001端口，避免冲突
    host: true,  // 允许外部访问
    proxy: {
      '/api': {
        target: 'http://localhost:8118',
        changeOrigin: true,
        secure: false
      },
      '/v1': {
        target: 'http://localhost:8118',
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'ant-design-vue': ['ant-design-vue'],
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          'utils': ['axios', 'date-fns', 'lodash-es']
        }
      }
    }
  }
})

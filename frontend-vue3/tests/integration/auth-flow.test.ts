// 认证流程集成测试
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import App from '@/App.vue'
import SignIn from '@/pages/SignIn.vue'
import DeviceTree from '@/pages/devices/DeviceTree.vue'
import { AuthAPI } from '@/api/auth'
import { StorageUtil } from '@/utils/storage'

// Mock dependencies
vi.mock('@/api/auth')
vi.mock('@/utils/storage')

// 创建测试路由
const routes = [
  { path: '/', component: DeviceTree, meta: { requiresAuth: true } },
  { path: '/login', component: SignIn, meta: { requiresAuth: false } }
]

describe('Authentication Flow Integration', () => {
  let router: any
  let pinia: any

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    
    router = createRouter({
      history: createWebHistory(),
      routes
    })

    vi.clearAllMocks()
  })

  it('should redirect to login when not authenticated', async () => {
    // Mock no stored authentication
    vi.mocked(StorageUtil.getAuthToken).mockReturnValue(null)
    vi.mocked(StorageUtil.getUserInfo).mockReturnValue(null)

    const wrapper = mount(App, {
      global: {
        plugins: [pinia, router],
        stubs: {
          'a-config-provider': true,
          'router-view': true
        }
      }
    })

    // 尝试访问受保护的路由
    await router.push('/')
    await router.isReady()

    // 应该重定向到登录页
    expect(router.currentRoute.value.path).toBe('/login')
  })

  it('should allow access to home when authenticated', async () => {
    // Mock stored authentication
    const mockUser = { email: '<EMAIL>', token: 'valid-token' }
    vi.mocked(StorageUtil.getAuthToken).mockReturnValue('valid-token')
    vi.mocked(StorageUtil.getUserInfo).mockReturnValue(mockUser)

    const wrapper = mount(App, {
      global: {
        plugins: [pinia, router],
        stubs: {
          'a-config-provider': true,
          'router-view': true
        }
      }
    })

    // 访问受保护的路由
    await router.push('/')
    await router.isReady()

    // 应该能够访问首页
    expect(router.currentRoute.value.path).toBe('/')
  })

  it('should complete login flow successfully', async () => {
    const mockUser = { email: '<EMAIL>', token: 'new-token' }
    vi.mocked(AuthAPI.login).mockResolvedValue(mockUser)

    const wrapper = mount(SignIn, {
      global: {
        plugins: [pinia, router],
        stubs: {
          'a-form': {
            template: '<form @submit.prevent="$emit(\'finish\', { email: \'<EMAIL>\', password: \'password\' })"><slot /></form>'
          },
          'a-form-item': { template: '<div><slot /></div>' },
          'a-input': { template: '<input />' },
          'a-input-password': { template: '<input type="password" />' },
          'a-button': { template: '<button type="submit"><slot /></button>' },
          'a-alert': { template: '<div class="alert"><slot /></div>' },
          'UserOutlined': { template: '<span>👤</span>' },
          'LockOutlined': { template: '<span>🔒</span>' }
        }
      }
    })

    // 模拟表单提交
    const form = wrapper.find('form')
    await form.trigger('submit')

    // 验证登录API被调用
    expect(AuthAPI.login).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password'
    })

    // 验证存储被调用
    expect(StorageUtil.setAuthToken).toHaveBeenCalledWith('new-token')
    expect(StorageUtil.setUserInfo).toHaveBeenCalledWith(mockUser)
  })

  it('should handle login failure correctly', async () => {
    const errorMessage = 'Invalid credentials'
    vi.mocked(AuthAPI.login).mockRejectedValue(new Error(errorMessage))

    const wrapper = mount(SignIn, {
      global: {
        plugins: [pinia, router],
        stubs: {
          'a-form': {
            template: '<form @submit.prevent="$emit(\'finish\', { email: \'<EMAIL>\', password: \'wrong\' })"><slot /></form>'
          },
          'a-form-item': { template: '<div><slot /></div>' },
          'a-input': { template: '<input />' },
          'a-input-password': { template: '<input type="password" />' },
          'a-button': { template: '<button type="submit"><slot /></button>' },
          'a-alert': { template: '<div class="alert"><slot /></div>' },
          'UserOutlined': { template: '<span>👤</span>' },
          'LockOutlined': { template: '<span>🔒</span>' }
        }
      }
    })

    // 模拟表单提交
    const form = wrapper.find('form')
    await form.trigger('submit')

    // 等待错误处理
    await wrapper.vm.$nextTick()

    // 验证错误状态
    expect(AuthAPI.login).toHaveBeenCalled()
    // 注意：由于我们使用了stub，实际的错误显示可能不会在DOM中体现
  })

  it('should logout and redirect to login', async () => {
    // Mock authenticated state
    const mockUser = { email: '<EMAIL>', token: 'valid-token' }
    vi.mocked(StorageUtil.getAuthToken).mockReturnValue('valid-token')
    vi.mocked(StorageUtil.getUserInfo).mockReturnValue(mockUser)

    const wrapper = mount(Home, {
      global: {
        plugins: [pinia, router],
        stubs: {
          'a-layout': { template: '<div><slot /></div>' },
          'a-layout-header': { template: '<div class="header"><slot /></div>' },
          'a-layout-content': { template: '<div class="content"><slot /></div>' },
          'a-card': { template: '<div class="card"><slot /></div>' },
          'a-space': { template: '<div><slot /></div>' },
          'a-button': { 
            template: '<button @click="$emit(\'click\')"><slot /></button>',
            emits: ['click']
          },
          'a-empty': { template: '<div class="empty"><slot /></div>' },
          'LogoutOutlined': { template: '<span>🚪</span>' },
          'ReloadOutlined': { template: '<span>🔄</span>' },
          'PlusOutlined': { template: '<span>➕</span>' },
          'DatabaseOutlined': { template: '<span>🗄️</span>' }
        }
      }
    })

    // 查找登出按钮并点击
    const logoutButton = wrapper.findAll('button').find(btn => 
      btn.text().includes('退出')
    )
    
    if (logoutButton) {
      await logoutButton.trigger('click')
      
      // 验证清除存储被调用
      expect(StorageUtil.clearAuth).toHaveBeenCalled()
    }
  })

  it('should validate token on app initialization', async () => {
    // Mock stored but potentially invalid token
    vi.mocked(StorageUtil.getAuthToken).mockReturnValue('stored-token')
    vi.mocked(StorageUtil.getUserInfo).mockReturnValue({ 
      email: '<EMAIL>', 
      token: 'stored-token' 
    })

    const wrapper = mount(App, {
      global: {
        plugins: [pinia, router],
        stubs: {
          'a-config-provider': true,
          'router-view': true
        }
      }
    })

    // 等待组件挂载和初始化
    await wrapper.vm.$nextTick()

    // 验证认证状态被初始化
    // 注意：由于我们mock了存储，实际的验证逻辑可能需要额外的设置
  })
})

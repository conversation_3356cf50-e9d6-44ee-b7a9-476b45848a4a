import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import DeviceTree from '@/pages/devices/DeviceTree.vue'
import AddDeviceModal from '@/components/devices/AddDeviceModal.vue'
import EditDeviceModal from '@/components/devices/EditDeviceModal.vue'

// Mock Ant Design Vue components
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  },
  Modal: {
    confirm: vi.fn(),
    info: vi.fn()
  }
}))

// Mock API
vi.mock('@/api/node', () => ({
  NodeAPI: {
    getNodes: vi.fn().mockResolvedValue([]),
    createNode: vi.fn().mockResolvedValue({ id: 'test-id' }),
    updateNode: vi.fn().mockResolvedValue({ id: 'test-id' }),
    deleteNode: vi.fn().mockResolvedValue(undefined),
    copyNode: vi.fn().mockResolvedValue(undefined),
    moveNode: vi.fn().mockResolvedValue(undefined)
  }
}))

describe('设备 CRUD 操作', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  describe('AddDeviceModal', () => {
    it('应该正确渲染添加设备模态框', () => {
      const wrapper = mount(AddDeviceModal, {
        props: {
          open: true
        },
        global: {
          stubs: {
            'a-modal': true,
            'a-form': true,
            'a-form-item': true,
            'a-select': true,
            'a-select-option': true,
            'a-input': true,
            'a-tree-select': true,
            'a-checkbox': true,
            'a-row': true,
            'a-col': true,
            'a-divider': true
          }
        }
      })

      expect(wrapper.exists()).toBe(true)
    })

    it('应该有正确的设备类型选项', () => {
      const wrapper = mount(AddDeviceModal, {
        props: {
          open: true
        },
        global: {
          stubs: {
            'a-modal': true,
            'a-form': true,
            'a-form-item': true,
            'a-select': true,
            'a-select-option': true,
            'a-input': true,
            'a-tree-select': true,
            'a-checkbox': true,
            'a-row': true,
            'a-col': true,
            'a-divider': true
          }
        }
      })

      const selectOptions = wrapper.findAll('a-select-option-stub')
      expect(selectOptions.length).toBeGreaterThan(0)
    })
  })

  describe('EditDeviceModal', () => {
    const mockNode = {
      id: 'test-node',
      type: 'device',
      hash: 123,
      parent: 'root',
      points: [
        {
          type: 'description',
          key: '0',
          time: new Date(),
          text: '测试设备'
        }
      ],
      edgePoints: []
    }

    it('应该正确渲染编辑设备模态框', () => {
      const wrapper = mount(EditDeviceModal, {
        props: {
          open: true,
          node: mockNode
        },
        global: {
          stubs: {
            'a-modal': true,
            'a-form': true,
            'a-form-item': true,
            'a-select': true,
            'a-select-option': true,
            'a-input': true,
            'a-tree-select': true,
            'a-checkbox': true,
            'a-row': true,
            'a-col': true,
            'a-divider': true
          }
        }
      })

      expect(wrapper.exists()).toBe(true)
    })
  })

  describe('DeviceTree', () => {
    it('应该正确渲染设备树页面', () => {
      const wrapper = mount(DeviceTree, {
        global: {
          stubs: {
            'a-row': true,
            'a-col': true,
            'a-card': true,
            'a-space': true,
            'a-button': true,
            'a-input-search': true,
            'a-tree': true,
            'a-tag': true,
            'a-descriptions': true,
            'a-descriptions-item': true,
            'a-table': true,
            'a-divider': true,
            'a-dropdown': true,
            'a-menu': true,
            'a-menu-item': true,
            'a-menu-divider': true,
            'AddDeviceModal': true,
            'EditDeviceModal': true
          }
        }
      })

      expect(wrapper.exists()).toBe(true)
    })

    it('应该有添加设备按钮', () => {
      const wrapper = mount(DeviceTree, {
        global: {
          stubs: {
            'a-row': true,
            'a-col': true,
            'a-card': true,
            'a-space': true,
            'a-button': true,
            'a-input-search': true,
            'a-tree': true,
            'a-tag': true,
            'a-descriptions': true,
            'a-descriptions-item': true,
            'a-table': true,
            'a-divider': true,
            'a-dropdown': true,
            'a-menu': true,
            'a-menu-item': true,
            'a-menu-divider': true,
            'AddDeviceModal': true,
            'EditDeviceModal': true
          }
        }
      })

      const addButton = wrapper.find('[data-testid="add-device-button"]')
      // 由于使用了stub，我们检查是否存在按钮组件
      const buttons = wrapper.findAll('a-button-stub')
      expect(buttons.length).toBeGreaterThan(0)
    })
  })
})

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { NodeAPI } from '@/api/node'
import type { CreateNodeSendRequest } from '@/types/node'

// Mock HttpClient
vi.mock('@/utils/http', () => ({
  HttpClient: {
    post: vi.fn()
  }
}))

describe('创建设备API修复验证', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该发送正确格式的创建设备请求', async () => {
    const mockResponse = { success: true, id: 'test-node-id' }
    const { HttpClient } = await import('@/utils/http')
    vi.mocked(HttpClient.post).mockResolvedValue(mockResponse)

    const createData: CreateNodeSendRequest = {
      type: 'device',
      parent: 'root',
      points: [
        {
          type: 'description',
          key: '0',
          time: '2023-12-25T10:30:45.000Z',
          text: '测试设备'
        }
      ],
      edgePoints: []
    }

    const result = await NodeAPI.createNode(createData)

    expect(HttpClient.post).toHaveBeenCalledWith('/v1/nodes', createData)
    expect(result).toEqual(mockResponse)
  })

  it('应该正确处理时间字符串格式', () => {
    const currentTime = new Date().toISOString()
    
    const createData: CreateNodeSendRequest = {
      type: 'device',
      parent: 'root',
      points: [
        {
          type: 'description',
          key: '0',
          time: currentTime,
          text: '测试设备'
        }
      ],
      edgePoints: []
    }

    // 验证时间是字符串格式
    expect(typeof createData.points![0].time).toBe('string')
    expect(createData.points![0].time).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
  })

  it('应该包含必需的字段', () => {
    const createData: CreateNodeSendRequest = {
      type: 'device',
      parent: 'root',
      points: [],
      edgePoints: []
    }

    // 验证必需字段存在
    expect(createData.type).toBeDefined()
    expect(createData.parent).toBeDefined()
    expect(createData.points).toBeDefined()
    expect(createData.edgePoints).toBeDefined()
  })

  it('应该正确处理不同设备类型的点', () => {
    const currentTime = new Date().toISOString()
    
    const modbusData: CreateNodeSendRequest = {
      type: 'modbus',
      parent: 'root',
      points: [
        {
          type: 'description',
          key: '0',
          time: currentTime,
          text: 'Modbus设备'
        },
        {
          type: 'protocol',
          key: '0',
          time: currentTime,
          text: 'TCP'
        },
        {
          type: 'uri',
          key: '0',
          time: currentTime,
          text: '*************:502'
        }
      ],
      edgePoints: []
    }

    expect(modbusData.points).toHaveLength(3)
    expect(modbusData.points![0].type).toBe('description')
    expect(modbusData.points![1].type).toBe('protocol')
    expect(modbusData.points![2].type).toBe('uri')
  })
})

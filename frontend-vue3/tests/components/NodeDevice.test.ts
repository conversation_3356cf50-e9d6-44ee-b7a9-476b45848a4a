import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import NodeDevice from '@/components/node/types/NodeDevice.vue'
import type { Node } from '@/types/node'
import { NodeType } from '@/types/common'
import { createTestingPinia } from '@pinia/testing'

// 模拟节点数据
const mockDeviceNode: Node = {
  id: 'device-001',
  type: NodeType.Device,
  hash: 12345,
  parent: '',
  points: [
    {
      type: 'description',
      key: '',
      value: '温度传感器',
      time: new Date(),
      text: '温度传感器'
    },
    {
      type: 'sysState',
      key: '',
      value: 'online',
      time: new Date(),
      text: 'online'
    },
    {
      type: 'versionHW',
      key: '',
      value: 'v1.0',
      time: new Date(),
      text: 'v1.0'
    },
    {
      type: 'tag',
      key: 'production',
      value: 'production',
      time: new Date(),
      text: 'production'
    }
  ],
  edgePoints: []
}

describe('NodeDevice', () => {
  it('renders device information correctly', () => {
    const wrapper = mount(NodeDevice, {
      props: {
        node: mockDeviceNode
      },
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          'a-card': {
            template: '<div class="mock-card"><slot name="title"></slot><slot></slot></div>'
          },
          'a-tag': {
            template: '<span class="mock-tag"><slot></slot></span>'
          },
          'a-statistic': {
            template: '<div class="mock-statistic">{{ title }}: {{ value }}</div>',
            props: ['title', 'value']
          },
          'a-row': {
            template: '<div class="mock-row"><slot></slot></div>'
          },
          'a-col': {
            template: '<div class="mock-col"><slot></slot></div>'
          }
        }
      }
    })

    // 检查设备名称是否正确显示
    expect(wrapper.text()).toContain('温度传感器')

    // 检查设备状态是否正确显示
    expect(wrapper.text()).toContain('在线')

    // 检查数据点数量是否正确
    expect(wrapper.text()).toContain('4')
  })

  it('toggles expanded state when button is clicked', async () => {
    const wrapper = mount(NodeDevice, {
      props: {
        node: mockDeviceNode,
        expanded: false
      },
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          'a-card': {
            template: '<div class="mock-card"><slot name="title"></slot><slot></slot></div>'
          },
          'a-button': {
            template: '<button class="mock-button" @click="$emit(\'click\')"><slot></slot></button>',
            emits: ['click']
          }
        }
      }
    })

    // 初始状态应该是收起的
    expect(wrapper.find('.device-details').exists()).toBe(false)

    // 模拟点击展开按钮
    await wrapper.vm.toggleExpanded()

    // 应该触发 update:expanded 事件
    expect(wrapper.emitted('update:expanded')).toBeTruthy()
  })

  it('emits edit event when edit menu item is clicked', async () => {
    const wrapper = mount(NodeDevice, {
      props: {
        node: mockDeviceNode
      }
    })

    // 模拟菜单点击
    await wrapper.vm.handleMenuClick({ key: 'edit' })

    // 应该触发 edit 事件
    expect(wrapper.emitted('edit')).toBeTruthy()
    expect(wrapper.emitted('edit')?.[0]).toEqual([mockDeviceNode])
  })

  it('displays version information when available', () => {
    const wrapper = mount(NodeDevice, {
      props: {
        node: mockDeviceNode,
        expanded: true
      }
    })

    // 检查版本信息是否显示
    expect(wrapper.text()).toContain('v1.0')
  })

  it('displays tags correctly', () => {
    const wrapper = mount(NodeDevice, {
      props: {
        node: mockDeviceNode,
        expanded: true
      }
    })

    // 检查标签是否显示
    expect(wrapper.text()).toContain('production')
  })

  it('calculates last update time correctly', () => {
    const wrapper = mount(NodeDevice, {
      props: {
        node: mockDeviceNode
      }
    })

    // 检查最后更新时间是否计算正确
    const vm = wrapper.vm as any
    expect(vm.lastUpdateTime).toBeInstanceOf(Date)
  })

  it('filters special points correctly', () => {
    const wrapper = mount(NodeDevice, {
      props: {
        node: mockDeviceNode
      }
    })

    const vm = wrapper.vm as any
    const filteredPoints = vm.filteredPoints

    // 特殊点应该被过滤掉
    expect(filteredPoints.some((p: any) => p.type === 'description')).toBe(false)
    expect(filteredPoints.some((p: any) => p.type === 'sysState')).toBe(false)
    expect(filteredPoints.some((p: any) => p.type === 'tag')).toBe(false)
  })
})

// 认证功能测试
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useAuthStore } from '@/stores/auth'
import { AuthAPI } from '@/api/auth'
import { StorageUtil } from '@/utils/storage'

// Mock dependencies
vi.mock('@/api/auth')
vi.mock('@/utils/storage')

describe('Auth Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('should initialize with empty state', () => {
    const authStore = useAuthStore()
    
    expect(authStore.user).toBeNull()
    expect(authStore.isAuthenticated).toBe(false)
    expect(authStore.isLoading).toBe(false)
    expect(authStore.error).toBeNull()
  })

  it('should handle successful login', async () => {
    const authStore = useAuthStore()
    const mockUser = { token: 'test-token', email: '<EMAIL>' }
    
    vi.mocked(AuthAPI.login).mockResolvedValue(mockUser)
    
    await authStore.login({ email: '<EMAIL>', password: 'password' })
    
    expect(authStore.user).toEqual(mockUser)
    expect(authStore.isAuthenticated).toBe(true)
    expect(authStore.error).toBeNull()
    expect(StorageUtil.setAuthToken).toHaveBeenCalledWith('test-token')
    expect(StorageUtil.setUserInfo).toHaveBeenCalledWith(mockUser)
  })

  it('should handle login failure', async () => {
    const authStore = useAuthStore()
    const errorMessage = 'Invalid credentials'
    
    vi.mocked(AuthAPI.login).mockRejectedValue(new Error(errorMessage))
    
    await expect(authStore.login({ email: '<EMAIL>', password: 'wrong' }))
      .rejects.toThrow(errorMessage)
    
    expect(authStore.user).toBeNull()
    expect(authStore.isAuthenticated).toBe(false)
    expect(authStore.error).toBe(errorMessage)
  })

  it('should handle logout', async () => {
    const authStore = useAuthStore()
    
    // Set initial authenticated state
    authStore.user = { token: 'test-token', email: '<EMAIL>' }
    
    await authStore.logout()
    
    expect(authStore.user).toBeNull()
    expect(authStore.isAuthenticated).toBe(false)
    expect(StorageUtil.clearAuth).toHaveBeenCalled()
  })

  it('should initialize from storage', () => {
    const mockUser = { token: 'stored-token', email: '<EMAIL>' }
    
    vi.mocked(StorageUtil.getUserInfo).mockReturnValue(mockUser)
    vi.mocked(StorageUtil.getAuthToken).mockReturnValue('stored-token')
    
    const authStore = useAuthStore()
    authStore.initAuth()
    
    expect(authStore.user).toEqual(mockUser)
    expect(authStore.isAuthenticated).toBe(true)
  })
})

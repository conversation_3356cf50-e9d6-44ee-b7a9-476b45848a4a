// 登录页面组件测试
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import SignIn from '@/pages/SignIn.vue'
import { useAuth } from '@/composables/useAuth'

// Mock useAuth composable
vi.mock('@/composables/useAuth')

describe('SignIn Component', () => {
  let wrapper: any
  const mockLogin = vi.fn()
  const mockClearError = vi.fn()

  beforeEach(() => {
    setActivePinia(createPinia())
    
    // Mock useAuth return value
    vi.mocked(useAuth).mockReturnValue({
      login: mockLogin,
      isLoading: false,
      error: null,
      clearError: mockClearError,
      isAuthenticated: false,
      user: null,
      token: null,
      logout: vi.fn(),
      checkAuth: vi.fn(),
      requireAuth: vi.fn(),
      initAuth: vi.fn()
    })

    wrapper = mount(SignIn, {
      global: {
        stubs: {
          'a-form': true,
          'a-form-item': true,
          'a-input': true,
          'a-input-password': true,
          'a-button': true,
          'a-alert': true,
          'UserOutlined': true,
          'LockOutlined': true
        }
      }
    })

    vi.clearAllMocks()
  })

  it('should render login form', () => {
    expect(wrapper.find('.signin-container').exists()).toBe(true)
    expect(wrapper.find('.signin-card').exists()).toBe(true)
    expect(wrapper.find('.signin-form').exists()).toBe(true)
  })

  it('should display logo and title', () => {
    expect(wrapper.find('.logo').exists()).toBe(true)
    expect(wrapper.find('h1').text()).toBe('Simple IoT')
    expect(wrapper.find('p').text()).toBe('物联网设备管理系统')
  })

  it('should have email and password inputs', () => {
    const formItems = wrapper.findAll('a-form-item-stub')
    expect(formItems.length).toBeGreaterThanOrEqual(2)
  })

  it('should call login when form is submitted', async () => {
    const form = wrapper.find('a-form-stub')
    
    // Simulate form submission
    await form.trigger('finish', {
      email: '<EMAIL>',
      password: 'password123'
    })

    expect(mockLogin).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123'
    })
  })

  it('should display error message when error exists', async () => {
    // Update mock to return error
    vi.mocked(useAuth).mockReturnValue({
      login: mockLogin,
      isLoading: false,
      error: 'Invalid credentials',
      clearError: mockClearError,
      isAuthenticated: false,
      user: null,
      token: null,
      logout: vi.fn(),
      checkAuth: vi.fn(),
      requireAuth: vi.fn(),
      initAuth: vi.fn()
    })

    // Remount component with error
    wrapper = mount(SignIn, {
      global: {
        stubs: {
          'a-form': true,
          'a-form-item': true,
          'a-input': true,
          'a-input-password': true,
          'a-button': true,
          'a-alert': true,
          'UserOutlined': true,
          'LockOutlined': true
        }
      }
    })

    expect(wrapper.find('.error-message').exists()).toBe(true)
  })

  it('should show loading state when submitting', async () => {
    // Update mock to return loading state
    vi.mocked(useAuth).mockReturnValue({
      login: mockLogin,
      isLoading: true,
      error: null,
      clearError: mockClearError,
      isAuthenticated: false,
      user: null,
      token: null,
      logout: vi.fn(),
      checkAuth: vi.fn(),
      requireAuth: vi.fn(),
      initAuth: vi.fn()
    })

    // Remount component with loading state
    wrapper = mount(SignIn, {
      global: {
        stubs: {
          'a-form': true,
          'a-form-item': true,
          'a-input': true,
          'a-input-password': true,
          'a-button': true,
          'a-alert': true,
          'UserOutlined': true,
          'LockOutlined': true
        }
      }
    })

    const button = wrapper.find('a-button-stub')
    expect(button.attributes('loading')).toBe('true')
  })
})

// 路由系统测试
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createRouter, createWebHistory } from 'vue-router'
import { setActivePinia, createPinia } from 'pinia'
import { useAuthStore } from '@/stores/auth'
import { StorageUtil } from '@/utils/storage'

// Mock dependencies
vi.mock('@/utils/storage')
vi.mock('@/stores/auth')

// 创建测试路由
const routes = [
  {
    path: '/',
    name: 'Home',
    component: { template: '<div>Home</div>' },
    meta: { requiresAuth: true, title: '首页' }
  },
  {
    path: '/login',
    name: 'SignIn',
    component: { template: '<div>Login</div>' },
    meta: { requiresAuth: false, title: '登录' }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: { template: '<div>404</div>' },
    meta: { requiresAuth: false, title: '页面未找到' }
  }
]

describe('Router System', () => {
  let router: any
  let authStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    
    // 创建路由实例
    router = createRouter({
      history: createWebHistory(),
      routes
    })

    // Mock auth store
    authStore = {
      isAuthenticated: false,
      initAuth: vi.fn(),
      validateToken: vi.fn().mockResolvedValue(true)
    }
    
    vi.mocked(useAuthStore).mockReturnValue(authStore)
    vi.clearAllMocks()
  })

  it('should create router with correct routes', () => {
    expect(router.getRoutes()).toHaveLength(3)
    expect(router.getRoutes().map((r: any) => r.name)).toEqual(['Home', 'SignIn', 'NotFound'])
  })

  it('should have correct route meta information', () => {
    const homeRoute = router.getRoutes().find((r: any) => r.name === 'Home')
    const loginRoute = router.getRoutes().find((r: any) => r.name === 'SignIn')
    
    expect(homeRoute?.meta.requiresAuth).toBe(true)
    expect(homeRoute?.meta.title).toBe('首页')
    
    expect(loginRoute?.meta.requiresAuth).toBe(false)
    expect(loginRoute?.meta.title).toBe('登录')
  })

  it('should redirect to login when accessing protected route without auth', async () => {
    // Mock no authentication
    vi.mocked(StorageUtil.getAuthToken).mockReturnValue(null)
    vi.mocked(StorageUtil.getUserInfo).mockReturnValue(null)
    
    const mockNext = vi.fn()
    const to = { meta: { requiresAuth: true }, name: 'Home' }
    const from = { name: 'SignIn' }
    
    // 这里我们模拟路由守卫的逻辑
    if (to.meta.requiresAuth && !authStore.isAuthenticated) {
      mockNext('/login')
    } else {
      mockNext()
    }
    
    expect(mockNext).toHaveBeenCalledWith('/login')
  })

  it('should allow access to protected route when authenticated', async () => {
    // Mock authentication
    authStore.isAuthenticated = true
    vi.mocked(StorageUtil.getAuthToken).mockReturnValue('valid-token')
    vi.mocked(StorageUtil.getUserInfo).mockReturnValue({ email: '<EMAIL>', token: 'valid-token' })
    
    const mockNext = vi.fn()
    const to = { meta: { requiresAuth: true }, name: 'Home' }
    const from = { name: 'SignIn' }
    
    // 模拟路由守卫逻辑
    if (to.meta.requiresAuth && !authStore.isAuthenticated) {
      mockNext('/login')
    } else {
      mockNext()
    }
    
    expect(mockNext).toHaveBeenCalledWith()
  })

  it('should redirect to home when accessing login page while authenticated', async () => {
    // Mock authentication
    authStore.isAuthenticated = true
    
    const mockNext = vi.fn()
    const to = { name: 'SignIn' }
    const from = { name: 'Home' }
    
    // 模拟路由守卫逻辑
    if (to.name === 'SignIn' && authStore.isAuthenticated) {
      mockNext('/')
    } else {
      mockNext()
    }
    
    expect(mockNext).toHaveBeenCalledWith('/')
  })

  it('should allow access to public routes without authentication', async () => {
    // Mock no authentication
    authStore.isAuthenticated = false
    
    const mockNext = vi.fn()
    const to = { meta: { requiresAuth: false }, name: 'SignIn' }
    const from = { name: 'Home' }
    
    // 模拟路由守卫逻辑
    if (to.meta.requiresAuth && !authStore.isAuthenticated) {
      mockNext('/login')
    } else {
      mockNext()
    }
    
    expect(mockNext).toHaveBeenCalledWith()
  })

  it('should validate token when accessing protected routes', async () => {
    // Mock stored authentication
    authStore.isAuthenticated = false
    vi.mocked(StorageUtil.getAuthToken).mockReturnValue('stored-token')
    vi.mocked(StorageUtil.getUserInfo).mockReturnValue({ email: '<EMAIL>', token: 'stored-token' })
    
    const mockNext = vi.fn()
    const to = { meta: { requiresAuth: true }, name: 'Home' }
    
    // 模拟路由守卫逻辑
    if (to.meta.requiresAuth && !authStore.isAuthenticated) {
      const token = StorageUtil.getAuthToken()
      const userInfo = StorageUtil.getUserInfo()
      
      if (token && userInfo) {
        authStore.initAuth()
        const isValid = await authStore.validateToken()
        if (isValid) {
          mockNext()
        } else {
          mockNext('/login')
        }
      } else {
        mockNext('/login')
      }
    } else {
      mockNext()
    }
    
    expect(authStore.initAuth).toHaveBeenCalled()
    expect(authStore.validateToken).toHaveBeenCalled()
    expect(mockNext).toHaveBeenCalledWith()
  })
})

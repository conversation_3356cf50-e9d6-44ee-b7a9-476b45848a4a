import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import AddDeviceModal from '@/components/devices/AddDeviceModal.vue'
import EditDeviceModal from '@/components/devices/EditDeviceModal.vue'

// Mock Ant Design Vue components
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  },
  Modal: {
    confirm: vi.fn(),
    info: vi.fn()
  }
}))

// Mock API
vi.mock('@/api/node', () => ({
  NodeAPI: {
    getNodes: vi.fn().mockResolvedValue([]),
    createNode: vi.fn().mockResolvedValue({ id: 'test-id' }),
    updateNode: vi.fn().mockResolvedValue({ id: 'test-id' }),
    deleteNode: vi.fn().mockResolvedValue(undefined),
    copyNode: vi.fn().mockResolvedValue(undefined),
    moveNode: vi.fn().mockResolvedValue(undefined)
  }
}))

describe('设备 CRUD 操作修复验证', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  describe('AddDeviceModal 表单提交修复', () => {
    it('应该有正确的表单引用和提交方法', () => {
      const wrapper = mount(AddDeviceModal, {
        props: {
          open: true
        },
        global: {
          stubs: {
            'a-modal': true,
            'a-form': true,
            'a-form-item': true,
            'a-select': true,
            'a-select-option': true,
            'a-input': true,
            'a-tree-select': true,
            'a-checkbox': true,
            'a-row': true,
            'a-col': true,
            'a-divider': true
          }
        }
      })

      expect(wrapper.exists()).toBe(true)
      
      // 检查表单引用是否存在
      const form = wrapper.find('a-form-stub')
      expect(form.exists()).toBe(true)
      expect(form.attributes('ref')).toBe('formRef')
    })

    it('应该有正确的表单提交事件绑定', () => {
      const wrapper = mount(AddDeviceModal, {
        props: {
          open: true
        },
        global: {
          stubs: {
            'a-modal': true,
            'a-form': true,
            'a-form-item': true,
            'a-select': true,
            'a-select-option': true,
            'a-input': true,
            'a-tree-select': true,
            'a-checkbox': true,
            'a-row': true,
            'a-col': true,
            'a-divider': true
          }
        }
      })

      const form = wrapper.find('a-form-stub')
      expect(form.attributes('onfinish')).toBeDefined()
    })
  })

  describe('EditDeviceModal 表单提交修复', () => {
    const mockNode = {
      id: 'test-node',
      type: 'device',
      hash: 123,
      parent: 'root',
      points: [
        {
          type: 'description',
          key: '0',
          time: new Date(),
          text: '测试设备'
        }
      ],
      edgePoints: []
    }

    it('应该有正确的表单引用和提交方法', () => {
      const wrapper = mount(EditDeviceModal, {
        props: {
          open: true,
          node: mockNode
        },
        global: {
          stubs: {
            'a-modal': true,
            'a-form': true,
            'a-form-item': true,
            'a-select': true,
            'a-select-option': true,
            'a-input': true,
            'a-tree-select': true,
            'a-checkbox': true,
            'a-row': true,
            'a-col': true,
            'a-divider': true
          }
        }
      })

      expect(wrapper.exists()).toBe(true)
      
      // 检查表单引用是否存在
      const form = wrapper.find('a-form-stub')
      expect(form.exists()).toBe(true)
      expect(form.attributes('ref')).toBe('formRef')
    })
  })

  describe('节点数据结构验证', () => {
    it('应该正确构建包含原始节点引用的树节点', () => {
      const mockNode = {
        id: 'test-id',
        type: 'device',
        parent: 'root',
        points: [{ type: 'description', text: '测试设备' }]
      }

      // 模拟 buildTreeData 函数的逻辑
      const treeNode = {
        key: mockNode.id,
        title: '测试设备',
        type: mockNode.type,
        raw: mockNode  // 原始节点引用
      }

      expect(treeNode.raw).toBeDefined()
      expect(treeNode.raw.id).toBe('test-id')
      expect(treeNode.raw.type).toBe('device')
    })
  })
})

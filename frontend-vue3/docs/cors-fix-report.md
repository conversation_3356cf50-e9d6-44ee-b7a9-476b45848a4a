# CORS 问题修复报告

**修复日期**: 2024-12-28  
**问题**: 前端登录请求被CORS策略阻止  
**状态**: ✅ 已修复

## 🔍 问题分析

### 错误信息
```
Access to XMLHttpRequest at 'http://localhost:8118/v1/auth' from origin 'http://127.0.0.1:3000' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

### 问题原因
1. **前端服务**: 运行在 `http://127.0.0.1:3000` (Vite开发服务器)
2. **后端服务**: 运行在 `http://localhost:8118` (Simple IoT后端)
3. **配置错误**: 前端直接请求后端URL，绕过了Vite代理
4. **CORS限制**: 浏览器阻止了跨域请求

### 配置问题
```typescript
// 错误的配置
VITE_API_URL=http://localhost:8118  // 直接指向后端
```

这导致axios直接请求 `http://localhost:8118/v1/auth`，触发CORS错误。

## 🛠️ 修复方案

### 1. Vite代理配置 ✅
```typescript
// vite.config.ts
server: {
  port: 3000,
  proxy: {
    '/api': {
      target: 'http://localhost:8118',  // 修复：8080 → 8118
      changeOrigin: true
    },
    '/v1': {
      target: 'http://localhost:8118',   // 修复：8080 → 8118
      changeOrigin: true
    }
  }
}
```

### 2. 环境变量修复 ✅
```bash
# .env.local
# 修复前
VITE_API_URL=http://localhost:8118

# 修复后
VITE_API_URL=  # 空值，使用相对路径
```

### 3. 请求流程修复
```
修复前: 前端 → 直接请求后端 → CORS错误
修复后: 前端 → Vite代理 → 后端 → 成功
```

## ✅ 修复验证

### 1. 后端API测试
```bash
curl -X POST -F "email=admin" -F "password=admin" http://localhost:8118/v1/auth
# ✅ 返回: {"token":"...","email":"admin"}
```

### 2. 代理测试
```bash
curl -X POST -F "email=admin" -F "password=admin" http://localhost:3000/v1/auth
# ✅ 返回: {"token":"...","email":"admin"}
```

### 3. 前端测试
- ✅ 登录页面加载正常
- ✅ 表单提交无CORS错误
- ✅ 认证请求成功代理到后端

## 📊 技术细节

### Vite代理工作原理
```
浏览器请求: http://localhost:3000/v1/auth
     ↓
Vite代理检测: /v1 路径匹配
     ↓
代理转发: http://localhost:8118/v1/auth
     ↓
后端响应: {"token":"...","email":"admin"}
     ↓
代理返回: 同域响应，无CORS问题
```

### 配置对比
| 配置项 | 修复前 | 修复后 | 说明 |
|--------|--------|--------|------|
| Vite代理目标 | 8080 | 8118 | 匹配后端实际端口 |
| API_URL | http://localhost:8118 | 空值 | 使用相对路径 |
| 请求方式 | 直接跨域 | 代理转发 | 避免CORS问题 |

## 🔧 相关配置文件

### 1. vite.config.ts
```typescript
proxy: {
  '/v1': {
    target: 'http://localhost:8118',  // Simple IoT后端端口
    changeOrigin: true
  }
}
```

### 2. .env.local
```bash
VITE_API_URL=  # 空值，使用相对路径通过代理
```

### 3. http.ts
```typescript
const http = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '',  // 空值时使用相对路径
  // ...
})
```

## 🚀 最佳实践

### 开发环境
- ✅ 使用Vite代理处理跨域请求
- ✅ 环境变量使用相对路径
- ✅ 代理目标指向正确的后端端口

### 生产环境
- 前端和后端部署在同一域名下，无CORS问题
- 或者在后端配置CORS头部支持跨域

### 调试技巧
1. **检查网络面板**: 确认请求URL和响应状态
2. **查看Vite日志**: 确认代理配置是否生效
3. **测试代理端点**: 使用curl验证代理功能

## 📝 总结

这次CORS问题的修复过程展示了：

1. **问题诊断**: 通过浏览器错误信息快速定位CORS问题
2. **配置审查**: 发现Vite代理端口配置错误
3. **环境变量**: 理解开发环境中相对路径的重要性
4. **测试验证**: 通过多层次测试确保修复有效

修复后的配置既解决了CORS问题，又保持了开发环境的灵活性和生产环境的兼容性。

---

*修复人: AI Assistant*  
*测试状态: 通过*  
*部署状态: 就绪*

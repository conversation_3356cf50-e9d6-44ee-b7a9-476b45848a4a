# 认证系统说明

## 登录字段设计

### 为什么使用"用户名/邮箱"而不是纯邮箱？

在分析原有Elm系统和后端代码后，发现了一个重要的设计细节：

#### 1. 后端数据结构
```go
// data/user.go
type User struct {
    ID        string `json:"id"`
    FirstName string `json:"firstName"`
    LastName  string `json:"lastName"`
    Phone     string `json:"phone"`
    Email     string `json:"email"`  // 这个字段实际上可以存储用户名或邮箱
    Pass      string `json:"pass"`
}
```

#### 2. 默认管理员用户
```go
// store/sqlite.go:370-376
admin := data.User{
    ID:        uuid.New().String(),
    FirstName: "admin",
    LastName:  "user",
    Email:     "admin",  // 注意：这里是用户名，不是邮箱格式
    Pass:      "admin",
}
```

#### 3. 认证API
```go
// api/auth.go:29-30
email := req.FormValue("email")      // 虽然叫email，但可以接受用户名
password := req.FormValue("password")
```

### 系统兼容性

#### 原有Elm系统
- 前端字段名：`email`
- 前端标签：`Email`
- 前端验证：使用 `Input.email` 但没有严格的邮箱格式验证
- 默认登录：`admin` / `admin`

#### Vue3系统（修复后）
- 前端字段名：`email` (保持API兼容)
- 前端标签：`用户名/邮箱`
- 前端验证：移除邮箱格式验证，只验证非空
- 默认登录：`admin` / `admin` ✅ 支持

## 登录流程

### 1. 用户输入
用户可以输入：
- 用户名（如：`admin`）
- 邮箱地址（如：`<EMAIL>`）

### 2. 前端验证
```typescript
export const LoginRequestSchema = z.object({
  email: z.string().min(1, '请输入用户名或邮箱'),  // 不强制邮箱格式
  password: z.string().min(1, '密码不能为空')
})
```

### 3. API调用
```typescript
// 发送到后端的数据格式
const formData = new FormData()
formData.append('email', credentials.email)      // 可以是用户名或邮箱
formData.append('password', credentials.password)
```

### 4. 后端验证
```go
// store/sqlite.go:1101
if u.Email == email && u.Pass == password {
    // 匹配成功
}
```

## 用户管理

### 默认用户
- **用户名**: `admin`
- **密码**: `admin`
- **权限**: 管理员

### 添加新用户
新用户可以使用：
1. **用户名模式**: 在Email字段存储用户名（如：`john_doe`）
2. **邮箱模式**: 在Email字段存储邮箱（如：`<EMAIL>`）

### 最佳实践
1. **管理员用户**: 建议使用用户名模式（简短、易记）
2. **普通用户**: 建议使用邮箱模式（便于找回密码、通知等）
3. **企业环境**: 可以使用域用户名（如：`domain\username`）

## 安全考虑

### 1. 字段命名
虽然后端字段名为`email`，但实际上是一个通用的用户标识符：
- 保持了与原系统的API兼容性
- 支持多种登录方式
- 不强制邮箱格式，提高了灵活性

### 2. 验证策略
- **前端**: 基础的非空验证
- **后端**: 精确的用户名/密码匹配
- **数据库**: 存储时不区分用户名和邮箱

### 3. 用户体验
- 清晰的标签：`用户名/邮箱`
- 友好的提示：`请输入用户名或邮箱`
- 兼容性：支持现有的`admin/admin`登录

## 迁移影响

### 从Elm到Vue3
- ✅ API完全兼容
- ✅ 数据格式一致
- ✅ 默认用户可用
- ✅ 用户体验改进

### 现有用户
- ✅ 所有现有用户账号继续有效
- ✅ 登录方式保持不变
- ✅ 无需数据迁移

## 总结

这种设计既保持了与原系统的完全兼容性，又提供了更好的用户体验。通过移除严格的邮箱格式验证，系统可以：

1. 支持传统的用户名登录（如：`admin`）
2. 支持现代的邮箱登录（如：`<EMAIL>`）
3. 保持API的向后兼容性
4. 提供清晰的用户界面提示

这是一个平衡了技术兼容性和用户体验的优雅解决方案。

# Token认证问题修复报告

**修复日期**: 2024-12-28  
**问题**: API请求返回401未授权错误，token未正确传递  
**状态**: ✅ 已修复

## 🔍 问题分析

### 发现的问题
1. **401错误**: `/v1/nodes` API请求返回401 Unauthorized
2. **Token传递问题**: Authorization header可能没有正确设置
3. **序列化不一致**: HTTP拦截器和StorageUtil使用不同的存储方式

### 问题原因分析

#### 1. Token验证端点不存在
```typescript
// 问题代码
static async validateToken(token: string): Promise<boolean> {
  await HttpClient.get('/api/auth/validate', {  // ❌ 后端没有这个端点
    headers: { Authorization: `Bearer ${token}` }
  })
}
```

Simple IoT后端没有专门的token验证端点，只能通过实际API调用来验证。

#### 2. 存储序列化不一致
```typescript
// HTTP拦截器 - 直接获取
const token = localStorage.getItem('auth_token')  // ❌ 可能获取到序列化的字符串

// StorageUtil - JSON序列化
static setItem(key: string, value: any): void {
  const serializedValue = JSON.stringify(value)  // 会对token进行JSON序列化
  localStorage.setItem(key, serializedValue)
}
```

这导致token可能被双重序列化，格式错误。

#### 3. 后端认证要求
根据后端代码分析：
```go
// api/key.go
func (k Key) Valid(req *http.Request) (bool, string) {
  fields := strings.Fields(req.Header.Get("Authorization"))
  if len(fields) < 2 {
    return false, ""
  }
  if fields[0] != "Bearer" {  // 必须是 "Bearer" 前缀
    return false, ""
  }
  // ...
}
```

## 🛠️ 修复方案

### 1. 修复Token验证方法 ✅

#### 使用现有API验证Token
```typescript
// 修复后
static async validateToken(token: string): Promise<boolean> {
  try {
    // 使用nodes API来验证token，因为后端没有专门的验证端点
    await HttpClient.get('/v1/nodes', {
      headers: { Authorization: `Bearer ${token}` }
    })
    return true
  } catch (error: any) {
    // 如果是401错误，说明token无效
    if (error.response?.status === 401) {
      return false
    }
    // 其他错误可能是网络问题，暂时认为token有效
    return true
  }
}
```

### 2. 修复存储一致性问题 ✅

#### 统一使用StorageUtil
```typescript
// 修复前 - HTTP拦截器
const token = localStorage.getItem('auth_token')  // ❌ 直接获取

// 修复后 - HTTP拦截器
import { StorageUtil } from './storage'

const token = StorageUtil.getAuthToken()  // ✅ 使用统一的存储工具
if (token) {
  config.headers.Authorization = `Bearer ${token}`
}
```

#### 统一错误处理
```typescript
// 修复前
localStorage.removeItem('auth_token')
localStorage.removeItem('user_info')

// 修复后
StorageUtil.clearAuth()  // 统一的清理方法
```

### 3. 简化不支持的功能 ✅

#### 登出功能
```typescript
// Simple IoT是无状态JWT认证，不需要服务端登出
static async logout(): Promise<void> {
  return Promise.resolve()  // 只需要清除本地存储
}
```

#### Token刷新
```typescript
// Simple IoT的JWT token有效期很长(168小时)，不需要刷新
static async refreshToken(): Promise<User> {
  throw new Error('Token refresh not supported, please login again')
}
```

## ✅ 修复验证

### 1. API调用测试
```bash
# 获取token
curl -X POST -F "email=admin" -F "password=admin" http://localhost:3000/v1/auth
# 返回: {"token":"eyJ...","email":"admin"}

# 使用token调用API
curl -H "Authorization: Bearer eyJ..." http://localhost:3000/v1/nodes
# 返回: 200 OK + 节点数据
```

### 2. 前端集成测试
- ✅ 登录成功后token正确保存
- ✅ HTTP拦截器正确添加Authorization header
- ✅ API请求不再返回401错误
- ✅ 设备列表和设备树正确显示数据

## 📊 技术细节

### Token流程
```
1. 用户登录 → POST /v1/auth
2. 后端返回JWT token
3. 前端保存到localStorage (通过StorageUtil)
4. HTTP拦截器自动添加到所有请求
5. 后端验证Bearer token
6. 返回授权的数据
```

### JWT Token特性
- **有效期**: 168小时 (7天)
- **算法**: HS256
- **签发者**: simpleiot
- **用户ID**: 存储在jti字段中

### 存储架构
```typescript
// 统一的存储接口
StorageUtil.setAuthToken(token: string)    // 保存token
StorageUtil.getAuthToken(): string | null  // 获取token
StorageUtil.clearAuth()                    // 清除认证信息
```

## 🔧 相关文件

### 修改的文件
- `src/utils/http.ts` - HTTP客户端拦截器
- `src/api/auth.ts` - 认证API方法
- `src/stores/auth.ts` - 认证状态管理

### 后端参考
- `api/key.go` - JWT token验证逻辑
- `api/auth.go` - 认证端点实现
- `api/nodes.go` - 节点API认证检查

## 🚀 最佳实践

### 1. 统一存储管理
- 所有localStorage操作都通过StorageUtil
- 避免直接使用localStorage API
- 确保序列化/反序列化一致性

### 2. 错误处理
- 401错误自动清除认证信息
- 网络错误不影响认证状态
- 提供用户友好的错误提示

### 3. 安全考虑
- Token存储在localStorage中
- 自动过期处理
- 页面刷新时恢复认证状态

## 📝 总结

这次修复解决了以下关键问题：

1. **Token验证**: 适配Simple IoT的认证机制
2. **存储一致性**: 统一localStorage操作
3. **API集成**: 正确的Authorization header格式
4. **错误处理**: 完善的401错误处理流程

修复后的系统现在能够：
- ✅ 正确保存和传递JWT token
- ✅ 成功调用需要认证的API
- ✅ 处理token过期和无效情况
- ✅ 提供流畅的用户认证体验

---

*修复人: AI Assistant*  
*测试状态: 通过*  
*认证集成: 完成*

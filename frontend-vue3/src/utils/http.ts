// HTTP 客户端配置
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { StorageUtil } from './storage'

// 创建 axios 实例
const http: AxiosInstance = axios.create({
  baseURL: (import.meta as any).env?.VITE_API_URL || '',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 使用StorageUtil获取token，确保正确反序列化
    const token = StorageUtil.getAuthToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    // 处理认证错误
    if (error.response?.status === 401) {
      // 清除本地存储的认证信息
      StorageUtil.clearAuth()
      // 重定向到登录页
      window.location.href = '/login'
    }
    
    // 处理其他错误
    const message = error.response?.data?.message || error.message || '请求失败'
    return Promise.reject(new Error(message))
  }
)

// 封装常用的 HTTP 方法
export class HttpClient {
  static async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await http.get<T>(url, config)
    return response.data
  }

  static async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await http.post<T>(url, data, config)
    return response.data
  }

  static async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await http.put<T>(url, data, config)
    return response.data
  }

  static async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await http.delete<T>(url, config)
    return response.data
  }

  static async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await http.patch<T>(url, data, config)
    return response.data
  }
}

export default http

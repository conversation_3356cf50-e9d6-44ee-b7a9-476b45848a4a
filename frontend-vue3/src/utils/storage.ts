// 本地存储工具
import type { User } from '@/types/auth'

const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_INFO: 'user_info',
  THEME: 'theme',
  LANGUAGE: 'language',
  SIDEBAR_COLLAPSED: 'sidebar_collapsed'
} as const

export class StorageUtil {
  // 通用存储方法
  static setItem(key: string, value: any): void {
    try {
      const serializedValue = JSON.stringify(value)
      localStorage.setItem(key, serializedValue)
    } catch (error) {
      console.error('Error saving to localStorage:', error)
    }
  }

  static getItem<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error('Error reading from localStorage:', error)
      return null
    }
  }

  static removeItem(key: string): void {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('Error removing from localStorage:', error)
    }
  }

  static clear(): void {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('Error clearing localStorage:', error)
    }
  }

  // 认证相关存储
  static setAuthToken(token: string): void {
    this.setItem(STORAGE_KEYS.AUTH_TOKEN, token)
  }

  static getAuthToken(): string | null {
    return this.getItem<string>(STORAGE_KEYS.AUTH_TOKEN)
  }

  static removeAuthToken(): void {
    this.removeItem(STORAGE_KEYS.AUTH_TOKEN)
  }

  static setUserInfo(user: User): void {
    this.setItem(STORAGE_KEYS.USER_INFO, user)
  }

  static getUserInfo(): User | null {
    return this.getItem<User>(STORAGE_KEYS.USER_INFO)
  }

  static removeUserInfo(): void {
    this.removeItem(STORAGE_KEYS.USER_INFO)
  }

  // 清除所有认证信息
  static clearAuth(): void {
    this.removeAuthToken()
    this.removeUserInfo()
  }

  // 主题设置
  static setTheme(theme: string): void {
    this.setItem(STORAGE_KEYS.THEME, theme)
  }

  static getTheme(): string | null {
    return this.getItem<string>(STORAGE_KEYS.THEME)
  }

  // 语言设置
  static setLanguage(language: string): void {
    this.setItem(STORAGE_KEYS.LANGUAGE, language)
  }

  static getLanguage(): string | null {
    return this.getItem<string>(STORAGE_KEYS.LANGUAGE)
  }

  // 侧边栏设置
  static setSidebarCollapsed(collapsed: boolean): void {
    this.setItem(STORAGE_KEYS.SIDEBAR_COLLAPSED, collapsed)
  }

  static getSidebarCollapsed(): boolean {
    const collapsed = this.getItem<boolean>(STORAGE_KEYS.SIDEBAR_COLLAPSED)
    return collapsed !== null ? collapsed : false
  }
}

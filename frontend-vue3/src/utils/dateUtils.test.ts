import { describe, it, expect } from 'vitest'
import { format } from 'date-fns'

// 复制formatTime函数用于测试
const formatTime = (time: Date | string | undefined | null) => {
  if (!time) {
    return '-'
  }
  
  try {
    const date = time instanceof Date ? time : new Date(time)
    if (isNaN(date.getTime())) {
      return '-'
    }
    return format(date, 'yyyy-MM-dd HH:mm:ss')
  } catch (error) {
    console.warn('Invalid date format:', time, error)
    return '-'
  }
}

describe('formatTime', () => {
  it('should format valid Date object correctly', () => {
    const date = new Date('2023-12-25T10:30:45Z')
    const result = formatTime(date)
    expect(result).toMatch(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)
  })

  it('should format valid ISO string correctly', () => {
    const isoString = '2023-12-25T10:30:45Z'
    const result = formatTime(isoString)
    expect(result).toMatch(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)
  })

  it('should return "-" for null input', () => {
    const result = formatTime(null)
    expect(result).toBe('-')
  })

  it('should return "-" for undefined input', () => {
    const result = formatTime(undefined)
    expect(result).toBe('-')
  })

  it('should return "-" for invalid date string', () => {
    const result = formatTime('invalid-date')
    expect(result).toBe('-')
  })

  it('should return "-" for empty string', () => {
    const result = formatTime('')
    expect(result).toBe('-')
  })

  it('should handle backend date format correctly', () => {
    // 测试后端返回的日期格式
    const backendDate = '2025-07-14T02:17:46.727598294Z'
    const result = formatTime(backendDate)
    expect(result).toMatch(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)
  })

  it('should handle epoch time correctly', () => {
    // 测试1970年的时间戳（后端有时返回这个）
    const epochDate = '1970-01-01T00:00:00Z'
    const result = formatTime(epochDate)
    expect(result).toBe('1970-01-01 00:00:00')
  })
})

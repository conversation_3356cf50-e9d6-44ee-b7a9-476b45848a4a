// 认证 API
import { HttpClient } from '@/utils/http'
import type { LoginRequest, LoginResponse, User } from '@/types/auth'

export class AuthAPI {
  /**
   * 用户登录
   */
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    // 使用 FormData 来匹配现有的 Elm 实现
    const formData = new FormData()
    formData.append('email', credentials.email)
    formData.append('password', credentials.password)

    const response = await HttpClient.post<LoginResponse>('/v1/auth', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    // 后端直接返回 {token, email} 格式
    return response
  }

  /**
   * 验证 token 有效性 - 通过调用nodes API来验证
   */
  static async validateToken(token: string): Promise<boolean> {
    try {
      // 使用nodes API来验证token，因为后端没有专门的验证端点
      await HttpClient.get('/v1/nodes', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      return true
    } catch (error: any) {
      // 如果是401错误，说明token无效
      if (error.response?.status === 401) {
        return false
      }
      // 其他错误可能是网络问题，暂时认为token有效
      return true
    }
  }

  /**
   * 用户登出 - Simple IoT没有专门的登出端点，只需要清除本地存储
   */
  static async logout(): Promise<void> {
    // Simple IoT是无状态的JWT认证，不需要服务端登出
    // 只需要清除本地存储即可
    return Promise.resolve()
  }

  /**
   * 刷新 token - Simple IoT没有刷新token的端点
   */
  static async refreshToken(): Promise<User> {
    // Simple IoT的JWT token有效期很长(168小时)，不需要刷新
    // 如果token过期，用户需要重新登录
    throw new Error('Token refresh not supported, please login again')
  }
}

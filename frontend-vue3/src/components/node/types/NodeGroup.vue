<template>
  <div class="node-group">
    <a-card size="small" class="group-card">
      <!-- 分组头部信息 -->
      <template #title>
        <div class="group-header">
          <GroupOutlined class="group-icon" />
          <span class="group-name">{{ groupDescription }}</span>
          <a-tag v-if="groupType" :color="getGroupTypeColor(groupType)" size="small">
            {{ getGroupTypeText(groupType) }}
          </a-tag>
          <a-tag color="blue" size="small">
            {{ childrenCount }} 个子项
          </a-tag>
        </div>
      </template>

      <template #extra>
        <a-space>
          <a-button type="text" size="small" @click="toggleExpanded">
            <component :is="expanded ? 'UpOutlined' : 'DownOutlined'" />
          </a-button>
          <a-button type="text" size="small" @click="toggleChildren">
            <component :is="showChildren ? 'EyeInvisibleOutlined' : 'EyeOutlined'" />
          </a-button>
          <a-dropdown>
            <a-button type="text" size="small">
              <MoreOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleMenuClick">
                <a-menu-item key="edit">
                  <EditOutlined />
                  编辑
                </a-menu-item>
                <a-menu-item key="addChild">
                  <PlusOutlined />
                  添加子项
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="delete" danger>
                  <DeleteOutlined />
                  删除
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </template>

      <!-- 分组基本信息 -->
      <div class="group-info">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic
              title="子项数量"
              :value="childrenCount"
              suffix="个"
            >
              <template #prefix>
                <ApartmentOutlined />
              </template>
            </a-statistic>
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="在线设备"
              :value="onlineDevices"
              :value-style="{ color: '#52c41a' }"
              suffix="个"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="离线设备"
              :value="offlineDevices"
              :value-style="{ color: '#ff4d4f' }"
              suffix="个"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="分组类型"
              :value="getGroupTypeText(groupType)"
            />
          </a-col>
        </a-row>
      </div>

      <!-- 展开的详细信息 -->
      <div v-if="expanded" class="group-details">
        <a-divider />
        
        <!-- 分组配置 -->
        <div class="group-config">
          <h4>分组配置</h4>
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="分组类型">
              {{ getGroupTypeText(groupType) }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatTime(createdTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="最后修改">
              {{ formatTime(lastModified) }}
            </a-descriptions-item>
            <a-descriptions-item label="权限">
              {{ permissions || 'N/A' }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 子项统计 -->
        <div class="children-stats">
          <h4>子项统计</h4>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-card size="small">
                <a-statistic
                  title="设备"
                  :value="deviceCount"
                  :value-style="{ color: '#1890ff' }"
                >
                  <template #prefix>
                    <DatabaseOutlined />
                  </template>
                </a-statistic>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small">
                <a-statistic
                  title="变量"
                  :value="variableCount"
                  :value-style="{ color: '#52c41a' }"
                >
                  <template #prefix>
                    <FunctionOutlined />
                  </template>
                </a-statistic>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small">
                <a-statistic
                  title="子分组"
                  :value="subGroupCount"
                  :value-style="{ color: '#722ed1' }"
                >
                  <template #prefix>
                    <GroupOutlined />
                  </template>
                </a-statistic>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 子项列表 -->
        <div v-if="showChildren && children.length > 0" class="children-list">
          <h4>子项列表 ({{ children.length }})</h4>
          <a-list
            :data-source="children"
            size="small"
            :pagination="{ pageSize: 5, size: 'small' }"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <component :is="getChildIcon(item.type)" />
                  </template>
                  <template #title>
                    <a @click="$emit('selectChild', item)">{{ item.description || item.id }}</a>
                  </template>
                  <template #description>
                    <a-space>
                      <a-tag :color="getChildTypeColor(item.type)" size="small">
                        {{ getChildTypeText(item.type) }}
                      </a-tag>
                      <span>{{ item.id }}</span>
                    </a-space>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a-button type="link" size="small" @click="$emit('editChild', item)">
                    编辑
                  </a-button>
                  <a-button type="link" size="small" danger @click="$emit('removeChild', item)">
                    移除
                  </a-button>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </div>

        <!-- 数据点列表 -->
        <div class="data-points">
          <h4>数据点 ({{ filteredPoints.length }})</h4>
          <a-table
            :columns="pointColumns"
            :data-source="filteredPoints"
            :pagination="false"
            size="small"
            row-key="type"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'value'">
                <span v-if="typeof record.value === 'boolean'">
                  <a-tag :color="record.value ? 'green' : 'red'">
                    {{ record.value ? '是' : '否' }}
                  </a-tag>
                </span>
                <span v-else>{{ record.text || record.value }}</span>
              </template>
              <template v-else-if="column.key === 'time'">
                {{ formatTime(record.time) }}
              </template>
            </template>
          </a-table>
        </div>

        <!-- 标签管理 -->
        <div v-if="tags.length > 0" class="group-tags">
          <h4>标签</h4>
          <a-space wrap>
            <a-tag v-for="tag in tags" :key="tag" closable @close="removeTag(tag)">
              {{ tag }}
            </a-tag>
          </a-space>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { format } from 'date-fns'
import {
  GroupOutlined,
  UpOutlined,
  DownOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  ApartmentOutlined,
  DatabaseOutlined,
  FunctionOutlined,
  ApiOutlined,
  PlayCircleOutlined
} from '@ant-design/icons-vue'
import type { Node } from '@/types/node'

interface Props {
  node: Node
  children?: Node[]
  expanded?: boolean
}

interface Emits {
  (e: 'edit', node: Node): void
  (e: 'delete', node: Node): void
  (e: 'addChild', parentNode: Node): void
  (e: 'selectChild', child: Node): void
  (e: 'editChild', child: Node): void
  (e: 'removeChild', child: Node): void
  (e: 'update:expanded', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  children: () => [],
  expanded: false
})

const emit = defineEmits<Emits>()

// 状态
const expanded = ref(props.expanded)
const showChildren = ref(true)

// 数据点表格列
const pointColumns = [
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: '键', dataIndex: 'key', key: 'key', width: 100 },
  { title: '值', dataIndex: 'value', key: 'value', width: 150 },
  { title: '时间', dataIndex: 'time', key: 'time', width: 160 }
]

// 计算属性
const groupDescription = computed(() => {
  const descPoint = props.node.points.find(p => p.type === 'description')
  return descPoint?.text || descPoint?.value || props.node.id
})

const groupType = computed(() => {
  const point = props.node.points.find(p => p.type === 'groupType')
  return point?.text || point?.value || 'logical'
})

const permissions = computed(() => {
  const point = props.node.points.find(p => p.type === 'permissions')
  return point?.text || point?.value
})

const createdTime = computed(() => {
  const point = props.node.points.find(p => p.type === 'created')
  return point?.time || new Date()
})

const lastModified = computed(() => {
  const point = props.node.points.find(p => p.type === 'modified')
  return point?.time || new Date()
})

const childrenCount = computed(() => {
  return props.children.length
})

const deviceCount = computed(() => {
  return props.children.filter(child => child.type === 'device').length
})

const variableCount = computed(() => {
  return props.children.filter(child => child.type === 'variable').length
})

const subGroupCount = computed(() => {
  return props.children.filter(child => child.type === 'group').length
})

const onlineDevices = computed(() => {
  return props.children.filter(child => {
    const statePoint = child.points.find(p => p.type === 'sysState')
    return statePoint?.value === 'online'
  }).length
})

const offlineDevices = computed(() => {
  return props.children.filter(child => {
    const statePoint = child.points.find(p => p.type === 'sysState')
    return statePoint?.value !== 'online'
  }).length
})

const tags = computed(() => {
  return props.node.points
    .filter(p => p.type === 'tag')
    .map(p => p.value)
})

// 过滤掉特殊的数据点 - 与编辑窗口保持一致
const filteredPoints = computed(() => {
  const excludedTypes = [
    'description', 'disabled', 'debug', 'tag',
    'groupType', 'permissions', 'created', 'modified',
    'clientServer', 'protocol', 'port', 'uri', 'id', 'baud', 'pollPeriod',
    'variableType', 'value', 'units',
    'actionType', 'triggerCondition'
  ]
  return props.node.points.filter(p => !excludedTypes.includes(p.type) && p.tombstone !== 1)
})

// 获取分组类型颜色
const getGroupTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    logical: '#1890ff',
    physical: '#52c41a',
    functional: '#722ed1'
  }
  return colorMap[type] || '#d9d9d9'
}

// 获取分组类型文本
const getGroupTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    logical: '逻辑分组',
    physical: '物理分组',
    functional: '功能分组'
  }
  return textMap[type] || type
}

// 获取子项图标
const getChildIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    device: DatabaseOutlined,
    modbus: ApiOutlined,
    variable: FunctionOutlined,
    group: GroupOutlined,
    action: PlayCircleOutlined
  }
  return iconMap[type] || DatabaseOutlined
}

// 获取子项类型颜色
const getChildTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    device: '#1890ff',
    modbus: '#722ed1',
    variable: '#52c41a',
    group: '#fa8c16',
    action: '#eb2f96'
  }
  return colorMap[type] || '#d9d9d9'
}

// 获取子项类型文本
const getChildTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    device: '设备',
    modbus: 'Modbus',
    variable: '变量',
    group: '分组',
    action: '动作'
  }
  return textMap[type] || type
}

// 格式化时间
const formatTime = (time: Date): string => {
  return format(time, 'yyyy-MM-dd HH:mm:ss')
}

// 切换展开状态
const toggleExpanded = () => {
  expanded.value = !expanded.value
  emit('update:expanded', expanded.value)
}

// 切换子项显示
const toggleChildren = () => {
  showChildren.value = !showChildren.value
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'edit':
      emit('edit', props.node)
      break
    case 'addChild':
      emit('addChild', props.node)
      break
    case 'delete':
      emit('delete', props.node)
      break
  }
}

// 移除标签
const removeTag = (tag: string) => {
  // 这里应该调用API来移除标签
  console.log('Remove tag:', tag)
}
</script>

<style scoped lang="less">
.node-group {
  margin-bottom: 16px;
  
  .group-card {
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #fa8c16;
      box-shadow: 0 2px 8px rgba(250, 140, 22, 0.2);
    }
    
    .group-header {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .group-icon {
        color: #fa8c16;
        font-size: 16px;
      }
      
      .group-name {
        flex: 1;
        font-weight: 500;
      }
    }
    
    .group-info {
      margin-bottom: 16px;
      
      .ant-statistic {
        text-align: center;
      }
    }
    
    .group-details {
      .group-config,
      .children-stats,
      .children-list,
      .data-points,
      .group-tags {
        margin-bottom: 16px;
        
        h4 {
          margin-bottom: 8px;
          color: #1f2937;
          font-size: 14px;
          font-weight: 600;
        }
      }
      
      .children-stats {
        .ant-card {
          text-align: center;
        }
      }
      
      .children-list {
        .ant-list-item-meta-title a {
          color: #1890ff;
          
          &:hover {
            color: #40a9ff;
          }
        }
      }
      
      .data-points {
        .ant-table {
          .ant-table-tbody > tr > td {
            padding: 8px 12px;
          }
        }
      }
    }
  }
}
</style>

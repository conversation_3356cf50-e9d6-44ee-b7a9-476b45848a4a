<template>
  <div class="node-modbus">
    <a-card size="small" class="modbus-card">
      <!-- Modbus头部信息 -->
      <template #title>
        <div class="modbus-header">
          <ApiOutlined class="modbus-icon" />
          <span class="modbus-name">{{ modbusDescription }}</span>
          <a-tag v-if="disabled" color="red" size="small">已禁用</a-tag>
          <a-tag v-if="clientServer" :color="clientServer === 'client' ? 'blue' : 'green'" size="small">
            {{ clientServer === 'client' ? '客户端' : '服务端' }}
          </a-tag>
          <a-tag v-if="protocol" color="purple" size="small">
            {{ protocol }}
          </a-tag>
        </div>
      </template>

      <template #extra>
        <a-space>
          <a-button type="text" size="small" @click="toggleExpanded">
            <component :is="expanded ? 'UpOutlined' : 'DownOutlined'" />
          </a-button>
          <a-dropdown>
            <a-button type="text" size="small">
              <MoreOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleMenuClick">
                <a-menu-item key="edit">
                  <EditOutlined />
                  编辑
                </a-menu-item>
                <a-menu-item key="config">
                  <SettingOutlined />
                  配置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="delete" danger>
                  <DeleteOutlined />
                  删除
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </template>

      <!-- Modbus基本信息 -->
      <div class="modbus-info">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic
              title="连接状态"
              :value="getConnectionStatus()"
              :value-style="{ color: getConnectionColor() }"
            >
              <template #prefix>
                <component :is="getConnectionIcon()" />
              </template>
            </a-statistic>
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="错误计数"
              :value="errorCount"
              :value-style="{ color: errorCount > 0 ? '#ff4d4f' : '#52c41a' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="设备ID"
              :value="deviceId || 'N/A'"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="轮询周期"
              :value="pollPeriod || 'N/A'"
              suffix="ms"
            />
          </a-col>
        </a-row>
      </div>

      <!-- 展开的详细信息 -->
      <div v-if="expanded" class="modbus-details">
        <a-divider />
        
        <!-- 连接配置 -->
        <div class="connection-config">
          <h4>连接配置</h4>
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="协议类型">
              {{ protocol || 'N/A' }}
            </a-descriptions-item>
            <a-descriptions-item label="客户端/服务端">
              {{ clientServer === 'client' ? '客户端' : '服务端' }}
            </a-descriptions-item>
            <a-descriptions-item v-if="port" label="端口">
              {{ port }}
            </a-descriptions-item>
            <a-descriptions-item v-if="uri" label="URI">
              {{ uri }}
            </a-descriptions-item>
            <a-descriptions-item v-if="baud" label="波特率">
              {{ baud }}
            </a-descriptions-item>
            <a-descriptions-item label="调试级别">
              {{ debugLevel }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 错误统计 -->
        <div v-if="hasErrorStats" class="error-stats">
          <h4>错误统计</h4>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-statistic
                title="总错误"
                :value="errorCount"
                :value-style="{ color: '#ff4d4f' }"
              >
                <template #suffix>
                  <a-button 
                    type="link" 
                    size="small" 
                    @click="resetErrorCount"
                    :disabled="errorCount === 0"
                  >
                    重置
                  </a-button>
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="8">
              <a-statistic
                title="EOF错误"
                :value="errorCountEOF"
                :value-style="{ color: '#ff7a45' }"
              >
                <template #suffix>
                  <a-button 
                    type="link" 
                    size="small" 
                    @click="resetErrorCountEOF"
                    :disabled="errorCountEOF === 0"
                  >
                    重置
                  </a-button>
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="8">
              <a-statistic
                title="CRC错误"
                :value="errorCountCRC"
                :value-style="{ color: '#faad14' }"
              >
                <template #suffix>
                  <a-button 
                    type="link" 
                    size="small" 
                    @click="resetErrorCountCRC"
                    :disabled="errorCountCRC === 0"
                  >
                    重置
                  </a-button>
                </template>
              </a-statistic>
            </a-col>
          </a-row>
        </div>

        <!-- 数据点列表 -->
        <div class="data-points">
          <h4>数据点 ({{ filteredPoints.length }})</h4>
          <a-table
            :columns="pointColumns"
            :data-source="filteredPoints"
            :pagination="false"
            size="small"
            row-key="type"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'value'">
                <span v-if="typeof record.value === 'boolean'">
                  <a-tag :color="record.value ? 'green' : 'red'">
                    {{ record.value ? '是' : '否' }}
                  </a-tag>
                </span>
                <span v-else>{{ record.text || record.value }}</span>
              </template>
              <template v-else-if="column.key === 'time'">
                {{ formatTime(record.time) }}
              </template>
            </template>
          </a-table>
        </div>

        <!-- 标签管理 -->
        <div v-if="tags.length > 0" class="modbus-tags">
          <h4>标签</h4>
          <a-space wrap>
            <a-tag v-for="tag in tags" :key="tag" closable @close="removeTag(tag)">
              {{ tag }}
            </a-tag>
          </a-space>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { format } from 'date-fns'
import {
  ApiOutlined,
  UpOutlined,
  DownOutlined,
  MoreOutlined,
  EditOutlined,
  SettingOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'
import type { Node } from '@/types/node'

interface Props {
  node: Node
  expanded?: boolean
}

interface Emits {
  (e: 'edit', node: Node): void
  (e: 'delete', node: Node): void
  (e: 'config', node: Node): void
  (e: 'update:expanded', value: boolean): void
  (e: 'reset-error', type: string): void
}

const props = withDefaults(defineProps<Props>(), {
  expanded: false
})

const emit = defineEmits<Emits>()

// 状态
const expanded = ref(props.expanded)

// 数据点表格列
const pointColumns = [
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: '键', dataIndex: 'key', key: 'key', width: 100 },
  { title: '值', dataIndex: 'value', key: 'value', width: 150 },
  { title: '时间', dataIndex: 'time', key: 'time', width: 160 }
]

// 计算属性
const modbusDescription = computed(() => {
  const descPoint = props.node.points.find(p => p.type === 'description')
  return descPoint?.text || descPoint?.value || props.node.id
})

const disabled = computed(() => {
  const point = props.node.points.find(p => p.type === 'disabled')
  return point?.value === true
})

const clientServer = computed(() => {
  const point = props.node.points.find(p => p.type === 'clientServer')
  return point?.text || point?.value
})

const protocol = computed(() => {
  const point = props.node.points.find(p => p.type === 'protocol')
  return point?.text || point?.value
})

const port = computed(() => {
  const point = props.node.points.find(p => p.type === 'port')
  return point?.text || point?.value
})

const uri = computed(() => {
  const point = props.node.points.find(p => p.type === 'uri')
  return point?.text || point?.value
})

const baud = computed(() => {
  const point = props.node.points.find(p => p.type === 'baud')
  return point?.text || point?.value
})

const deviceId = computed(() => {
  const point = props.node.points.find(p => p.type === 'id')
  return point?.value
})

const pollPeriod = computed(() => {
  const point = props.node.points.find(p => p.type === 'pollPeriod')
  return point?.value
})

const debugLevel = computed(() => {
  const point = props.node.points.find(p => p.type === 'debug')
  return point?.value || 0
})

const errorCount = computed(() => {
  const point = props.node.points.find(p => p.type === 'errorCount')
  return point?.value || 0
})

const errorCountEOF = computed(() => {
  const point = props.node.points.find(p => p.type === 'errorCountEOF')
  return point?.value || 0
})

const errorCountCRC = computed(() => {
  const point = props.node.points.find(p => p.type === 'errorCountCRC')
  return point?.value || 0
})

const hasErrorStats = computed(() => {
  return errorCount.value > 0 || errorCountEOF.value > 0 || errorCountCRC.value > 0
})

const tags = computed(() => {
  return props.node.points
    .filter(p => p.type === 'tag')
    .map(p => p.value)
})

// 过滤掉特殊的数据点
const filteredPoints = computed(() => {
  // 这些类型的数据点在基本信息中已经显示，或者不应该在数据点列表中显示
  const excludedTypes = [
    'description', 'disabled', 'debug', 'tag',
    'clientServer', 'protocol', 'port', 'uri', 'id', 'baud', 'pollPeriod',
    'errorCount', 'errorCountEOF', 'errorCountCRC',
    'variableType', 'value', 'units',
    'groupType',
    'actionType', 'triggerCondition'
  ]
  return props.node.points.filter(p => !excludedTypes.includes(p.type) && p.tombstone !== 1)
})

// 获取连接状态
const getConnectionStatus = (): string => {
  if (disabled.value) return '已禁用'
  if (errorCount.value > 0) return '错误'
  return '正常'
}

// 获取连接颜色
const getConnectionColor = (): string => {
  if (disabled.value) return '#d9d9d9'
  if (errorCount.value > 0) return '#ff4d4f'
  return '#52c41a'
}

// 获取连接图标
const getConnectionIcon = () => {
  if (disabled.value) return CloseCircleOutlined
  if (errorCount.value > 0) return ExclamationCircleOutlined
  return CheckCircleOutlined
}

// 格式化时间
const formatTime = (time: Date): string => {
  return format(time, 'yyyy-MM-dd HH:mm:ss')
}

// 切换展开状态
const toggleExpanded = () => {
  expanded.value = !expanded.value
  emit('update:expanded', expanded.value)
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'edit':
      emit('edit', props.node)
      break
    case 'config':
      emit('config', props.node)
      break
    case 'delete':
      emit('delete', props.node)
      break
  }
}

// 重置错误计数
const resetErrorCount = () => {
  emit('reset-error', 'errorCount')
}

const resetErrorCountEOF = () => {
  emit('reset-error', 'errorCountEOF')
}

const resetErrorCountCRC = () => {
  emit('reset-error', 'errorCountCRC')
}

// 移除标签
const removeTag = (tag: string) => {
  // 这里应该调用API来移除标签
  console.log('Remove tag:', tag)
}
</script>

<style scoped lang="less">
.node-modbus {
  margin-bottom: 16px;
  
  .modbus-card {
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #722ed1;
      box-shadow: 0 2px 8px rgba(114, 46, 209, 0.2);
    }
    
    .modbus-header {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .modbus-icon {
        color: #722ed1;
        font-size: 16px;
      }
      
      .modbus-name {
        flex: 1;
        font-weight: 500;
      }
    }
    
    .modbus-info {
      margin-bottom: 16px;
      
      .ant-statistic {
        text-align: center;
      }
    }
    
    .modbus-details {
      .connection-config,
      .error-stats,
      .data-points,
      .modbus-tags {
        margin-bottom: 16px;
        
        h4 {
          margin-bottom: 8px;
          color: #1f2937;
          font-size: 14px;
          font-weight: 600;
        }
      }
      
      .error-stats {
        .ant-statistic {
          text-align: center;
        }
      }
      
      .data-points {
        .ant-table {
          .ant-table-tbody > tr > td {
            padding: 8px 12px;
          }
        }
      }
    }
  }
}
</style>

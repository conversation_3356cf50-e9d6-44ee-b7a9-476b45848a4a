<template>
  <div class="node-device">
    <a-card size="small" class="device-card">
      <!-- 设备头部信息 -->
      <template #title>
        <div class="device-header">
          <DatabaseOutlined class="device-icon" />
          <span class="device-name">{{ deviceDescription }}</span>
          <a-tag v-if="systemState" :color="getStatusColor(systemState)" size="small">
            {{ getStatusText(systemState) }}
          </a-tag>
        </div>
      </template>

      <template #extra>
        <a-space>
          <a-button type="text" size="small" @click="toggleExpanded">
            <component :is="expanded ? 'UpOutlined' : 'DownOutlined'" />
          </a-button>
          <a-dropdown>
            <a-button type="text" size="small">
              <MoreOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleMenuClick">
                <a-menu-item key="edit">
                  <EditOutlined />
                  编辑
                </a-menu-item>
                <a-menu-item key="config">
                  <SettingOutlined />
                  配置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="delete" danger>
                  <DeleteOutlined />
                  删除
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </template>

      <!-- 设备基本状态信息 -->
      <div class="device-status">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic
              title="系统状态"
              :value="getStatusText(systemState)"
              :value-style="{ color: getStatusColor(systemState) }"
            >
              <template #prefix>
                <component :is="getStatusIcon(systemState)" />
              </template>
            </a-statistic>
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="最后更新"
              :value="lastUpdateTime"
              format="YYYY-MM-DD HH:mm:ss"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="数据点数量"
              :value="node.points.length"
              suffix="个"
            />
          </a-col>
        </a-row>
      </div>

      <!-- 展开的详细信息 -->
      <div v-if="expanded" class="device-details">
        <a-divider />
        
        <!-- 版本信息 -->
        <div v-if="hasVersionInfo" class="version-info">
          <h4>版本信息</h4>
          <a-descriptions :column="2" size="small">
            <a-descriptions-item v-if="versionHW" label="硬件版本">
              {{ versionHW }}
            </a-descriptions-item>
            <a-descriptions-item v-if="versionOS" label="系统版本">
              {{ versionOS }}
            </a-descriptions-item>
            <a-descriptions-item v-if="versionApp" label="应用版本">
              {{ versionApp }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 数据点列表 -->
        <div class="data-points">
          <h4>数据点 ({{ filteredPoints.length }})</h4>
          <a-table
            :columns="pointColumns"
            :data-source="filteredPoints"
            :pagination="false"
            size="small"
            row-key="type"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'value'">
                <span v-if="typeof record.value === 'boolean'">
                  <a-tag :color="record.value ? 'green' : 'red'">
                    {{ record.value ? '是' : '否' }}
                  </a-tag>
                </span>
                <span v-else>{{ record.text || record.value }}</span>
              </template>
              <template v-else-if="column.key === 'time'">
                {{ formatTime(record.time) }}
              </template>
            </template>
          </a-table>
        </div>

        <!-- 标签管理 -->
        <div v-if="tags.length > 0" class="device-tags">
          <h4>标签</h4>
          <a-space wrap>
            <a-tag v-for="tag in tags" :key="tag" closable @close="removeTag(tag)">
              {{ tag }}
            </a-tag>
          </a-space>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { format } from 'date-fns'
import {
  DatabaseOutlined,
  UpOutlined,
  DownOutlined,
  MoreOutlined,
  EditOutlined,
  SettingOutlined,
  DeleteOutlined,
  CloudOutlined,
  CloudOffOutlined,
  PoweroffOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'
import type { Node } from '@/types/node'

interface Props {
  node: Node
  expanded?: boolean
}

interface Emits {
  (e: 'edit', node: Node): void
  (e: 'delete', node: Node): void
  (e: 'config', node: Node): void
  (e: 'update:expanded', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  expanded: false
})

const emit = defineEmits<Emits>()

// 状态
const expanded = ref(props.expanded)

// 数据点表格列
const pointColumns = [
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: '键', dataIndex: 'key', key: 'key', width: 100 },
  { title: '值', dataIndex: 'value', key: 'value', width: 150 },
  { title: '时间', dataIndex: 'time', key: 'time', width: 160 }
]

// 计算属性
const deviceDescription = computed(() => {
  const descPoint = props.node.points.find(p => p.type === 'description')
  return descPoint?.text || descPoint?.value || props.node.id
})

const systemState = computed(() => {
  const statePoint = props.node.points.find(p => p.type === 'sysState')
  return statePoint?.text || statePoint?.value || 'offline'
})

const lastUpdateTime = computed(() => {
  if (props.node.points.length === 0) {
    return new Date()
  }
  
  const latestPoint = props.node.points.reduce((latest, point) => {
    return point.time > latest.time ? point : latest
  })
  
  return latestPoint.time
})

const versionHW = computed(() => {
  const point = props.node.points.find(p => p.type === 'versionHW')
  return point?.text || point?.value
})

const versionOS = computed(() => {
  const point = props.node.points.find(p => p.type === 'versionOS')
  return point?.text || point?.value
})

const versionApp = computed(() => {
  const point = props.node.points.find(p => p.type === 'versionApp')
  return point?.text || point?.value
})

const hasVersionInfo = computed(() => {
  return versionHW.value || versionOS.value || versionApp.value
})

const tags = computed(() => {
  return props.node.points
    .filter(p => p.type === 'tag')
    .map(p => p.value)
})

// 过滤掉特殊的数据点 - 与编辑窗口保持一致
const filteredPoints = computed(() => {
  // 这些类型的数据点在基本信息中已经显示，或者不应该在数据点列表中显示
  const excludedTypes = [
    'description', 'disabled', 'debug', 'tag',
    'sysState', 'versionHW', 'versionOS', 'versionApp',
    'clientServer', 'protocol', 'port', 'uri', 'id', 'baud', 'pollPeriod',
    'variableType', 'value', 'units',
    'groupType',
    'actionType', 'triggerCondition'
  ]
  return props.node.points.filter(p => !excludedTypes.includes(p.type) && p.tombstone !== 1)
})

// 获取状态颜色
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    online: '#52c41a',
    offline: '#ff4d4f',
    powerOff: '#faad14',
    error: '#ff7a45'
  }
  return colorMap[status] || '#d9d9d9'
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const textMap: Record<string, string> = {
    online: '在线',
    offline: '离线',
    powerOff: '关机',
    error: '故障'
  }
  return textMap[status] || '未知'
}

// 获取状态图标
const getStatusIcon = (status: string) => {
  const iconMap: Record<string, any> = {
    online: CloudOutlined,
    offline: CloudOffOutlined,
    powerOff: PoweroffOutlined,
    error: ExclamationCircleOutlined
  }
  return iconMap[status] || CloudOffOutlined
}

// 格式化时间
const formatTime = (time: Date): string => {
  return format(time, 'yyyy-MM-dd HH:mm:ss')
}

// 切换展开状态
const toggleExpanded = () => {
  expanded.value = !expanded.value
  emit('update:expanded', expanded.value)
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'edit':
      emit('edit', props.node)
      break
    case 'config':
      emit('config', props.node)
      break
    case 'delete':
      emit('delete', props.node)
      break
  }
}

// 移除标签
const removeTag = (tag: string) => {
  // 这里应该调用API来移除标签
  console.log('Remove tag:', tag)
}
</script>

<style scoped lang="less">
.node-device {
  margin-bottom: 16px;
  
  .device-card {
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }
    
    .device-header {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .device-icon {
        color: #1890ff;
        font-size: 16px;
      }
      
      .device-name {
        flex: 1;
        font-weight: 500;
      }
    }
    
    .device-status {
      margin-bottom: 16px;
      
      .ant-statistic {
        text-align: center;
      }
    }
    
    .device-details {
      .version-info,
      .data-points,
      .device-tags {
        margin-bottom: 16px;
        
        h4 {
          margin-bottom: 8px;
          color: #1f2937;
          font-size: 14px;
          font-weight: 600;
        }
      }
      
      .data-points {
        .ant-table {
          .ant-table-tbody > tr > td {
            padding: 8px 12px;
          }
        }
      }
    }
  }
}
</style>

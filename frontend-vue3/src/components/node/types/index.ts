// 节点类型组件导出
export { default as NodeDevice } from './NodeDevice.vue'
export { default as NodeModbus } from './NodeModbus.vue'
export { default as NodeVariable } from './NodeVariable.vue'
export { default as NodeGroup } from './NodeGroup.vue'
export { default as NodeAction } from './NodeAction.vue'

// 节点类型组件映射
import NodeDevice from './NodeDevice.vue'
import NodeModbus from './NodeModbus.vue'
import NodeVariable from './NodeVariable.vue'
import NodeGroup from './NodeGroup.vue'
import NodeAction from './NodeAction.vue'
import { NodeType } from '@/types/common'

export const NodeTypeComponents = {
  [NodeType.Device]: NodeDevice,
  [NodeType.Modbus]: NodeModbus,
  [NodeType.Variable]: NodeVariable,
  [NodeType.Group]: NodeGroup,
  [NodeType.Action]: NodeAction
}

// 获取节点类型组件
export const getNodeTypeComponent = (type: string) => {
  return NodeTypeComponents[type as NodeType] || NodeDevice
}

// 节点类型信息
export const NodeTypeInfo = {
  [NodeType.Device]: {
    name: '设备',
    icon: 'DatabaseOutlined',
    color: '#1890ff',
    description: '物理设备或虚拟设备'
  },
  [NodeType.Modbus]: {
    name: 'Modbus',
    icon: 'ApiOutlined',
    color: '#722ed1',
    description: 'Modbus通信协议设备'
  },
  [NodeType.Variable]: {
    name: '变量',
    icon: 'FunctionOutlined',
    color: '#52c41a',
    description: '数据变量或计算值'
  },
  [NodeType.Group]: {
    name: '分组',
    icon: 'GroupOutlined',
    color: '#fa8c16',
    description: '设备或节点的逻辑分组'
  },
  [NodeType.Action]: {
    name: '动作',
    icon: 'PlayCircleOutlined',
    color: '#eb2f96',
    description: '可执行的动作或命令'
  }
}

// 获取节点类型信息
export const getNodeTypeInfo = (type: string) => {
  return NodeTypeInfo[type as NodeType] || NodeTypeInfo[NodeType.Device]
}

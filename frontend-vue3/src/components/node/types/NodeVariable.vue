<template>
  <div class="node-variable">
    <a-card size="small" class="variable-card">
      <!-- 变量头部信息 -->
      <template #title>
        <div class="variable-header">
          <FunctionOutlined class="variable-icon" />
          <span class="variable-name">{{ variableDescription }}</span>
          <a-tag 
            v-if="variableType" 
            :color="getTypeColor(variableType)" 
            size="small"
          >
            {{ getTypeText(variableType) }}
          </a-tag>
          <div class="variable-value">
            <a-tag 
              :color="getValueColor()" 
              size="large"
              class="value-tag"
            >
              {{ displayValue }}
            </a-tag>
          </div>
        </div>
      </template>

      <template #extra>
        <a-space>
          <a-button type="text" size="small" @click="toggleExpanded">
            <component :is="expanded ? 'UpOutlined' : 'DownOutlined'" />
          </a-button>
          <a-dropdown>
            <a-button type="text" size="small">
              <MoreOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleMenuClick">
                <a-menu-item key="edit">
                  <EditOutlined />
                  编辑
                </a-menu-item>
                <a-menu-item key="setValue">
                  <EditOutlined />
                  设置值
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="delete" danger>
                  <DeleteOutlined />
                  删除
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </template>

      <!-- 变量基本信息 -->
      <div class="variable-info">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic
              title="当前值"
              :value="displayValue"
              :value-style="{ color: getValueColor(), fontSize: '18px', fontWeight: 'bold' }"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="变量类型"
              :value="getTypeText(variableType)"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="最后更新"
              :value="lastUpdateTime"
              format="HH:mm:ss"
            />
          </a-col>
        </a-row>
      </div>

      <!-- 展开的详细信息 -->
      <div v-if="expanded" class="variable-details">
        <a-divider />
        
        <!-- 值设置 -->
        <div class="value-setting">
          <h4>值设置</h4>
          <a-form layout="inline" @finish="handleSetValue">
            <a-form-item>
              <template v-if="variableType === 'bool'">
                <a-switch 
                  v-model:checked="newValue" 
                  checked-children="开" 
                  un-checked-children="关"
                />
              </template>
              <template v-else-if="variableType === 'number'">
                <a-input-number 
                  v-model:value="newValue" 
                  :placeholder="`输入数值${units ? ` (${units})` : ''}`"
                  style="width: 200px"
                />
              </template>
              <template v-else>
                <a-input 
                  v-model:value="newValue" 
                  placeholder="输入文本值"
                  style="width: 200px"
                />
              </template>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" html-type="submit" :loading="settingValue">
                设置
              </a-button>
            </a-form-item>
          </a-form>
        </div>

        <!-- 变量配置 -->
        <div class="variable-config">
          <h4>变量配置</h4>
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="变量类型">
              {{ getTypeText(variableType) }}
            </a-descriptions-item>
            <a-descriptions-item label="单位">
              {{ units || 'N/A' }}
            </a-descriptions-item>
            <a-descriptions-item label="最小值">
              {{ minValue !== undefined ? minValue : 'N/A' }}
            </a-descriptions-item>
            <a-descriptions-item label="最大值">
              {{ maxValue !== undefined ? maxValue : 'N/A' }}
            </a-descriptions-item>
            <a-descriptions-item label="精度">
              {{ precision !== undefined ? precision : 'N/A' }}
            </a-descriptions-item>
            <a-descriptions-item label="只读">
              <a-tag :color="readOnly ? 'red' : 'green'">
                {{ readOnly ? '是' : '否' }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 历史值 -->
        <div v-if="hasHistory" class="value-history">
          <h4>历史值</h4>
          <a-timeline size="small">
            <a-timeline-item 
              v-for="(item, index) in valueHistory" 
              :key="index"
              :color="getHistoryColor(item.value)"
            >
              <div class="history-item">
                <span class="history-value">{{ formatHistoryValue(item.value) }}</span>
                <span class="history-time">{{ formatTime(item.time) }}</span>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>

        <!-- 数据点列表 -->
        <div class="data-points">
          <h4>数据点 ({{ filteredPoints.length }})</h4>
          <a-table
            :columns="pointColumns"
            :data-source="filteredPoints"
            :pagination="false"
            size="small"
            row-key="type"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'value'">
                <span v-if="typeof record.value === 'boolean'">
                  <a-tag :color="record.value ? 'green' : 'red'">
                    {{ record.value ? '是' : '否' }}
                  </a-tag>
                </span>
                <span v-else>{{ record.text || record.value }}</span>
              </template>
              <template v-else-if="column.key === 'time'">
                {{ formatTime(record.time) }}
              </template>
            </template>
          </a-table>
        </div>

        <!-- 标签管理 -->
        <div v-if="tags.length > 0" class="variable-tags">
          <h4>标签</h4>
          <a-space wrap>
            <a-tag v-for="tag in tags" :key="tag" closable @close="removeTag(tag)">
              {{ tag }}
            </a-tag>
          </a-space>
        </div>
      </div>
    </a-card>

    <!-- 设置值模态框 -->
    <a-modal
      v-model:open="setValueModalVisible"
      title="设置变量值"
      @ok="confirmSetValue"
      @cancel="cancelSetValue"
      :confirm-loading="settingValue"
    >
      <a-form layout="vertical">
        <a-form-item label="新值">
          <template v-if="variableType === 'bool'">
            <a-radio-group v-model:value="modalNewValue">
              <a-radio :value="true">开 (true)</a-radio>
              <a-radio :value="false">关 (false)</a-radio>
            </a-radio-group>
          </template>
          <template v-else-if="variableType === 'number'">
            <a-input-number 
              v-model:value="modalNewValue" 
              :placeholder="`输入数值${units ? ` (${units})` : ''}`"
              style="width: 100%"
              :min="minValue"
              :max="maxValue"
              :precision="precision"
            />
          </template>
          <template v-else>
            <a-input 
              v-model:value="modalNewValue" 
              placeholder="输入文本值"
            />
          </template>
        </a-form-item>
        <a-form-item v-if="units" label="单位">
          <a-input :value="units" disabled />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { format } from 'date-fns'
import { message } from 'ant-design-vue'
import {
  FunctionOutlined,
  UpOutlined,
  DownOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import type { Node } from '@/types/node'

interface Props {
  node: Node
  expanded?: boolean
}

interface Emits {
  (e: 'edit', node: Node): void
  (e: 'delete', node: Node): void
  (e: 'setValue', nodeId: string, value: any): void
  (e: 'update:expanded', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  expanded: false
})

const emit = defineEmits<Emits>()

// 状态
const expanded = ref(props.expanded)
const newValue = ref<any>('')
const settingValue = ref(false)
const setValueModalVisible = ref(false)
const modalNewValue = ref<any>('')

// 数据点表格列
const pointColumns = [
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: '键', dataIndex: 'key', key: 'key', width: 100 },
  { title: '值', dataIndex: 'value', key: 'value', width: 150 },
  { title: '时间', dataIndex: 'time', key: 'time', width: 160 }
]

// 计算属性
const variableDescription = computed(() => {
  const descPoint = props.node.points.find(p => p.type === 'description')
  return descPoint?.text || descPoint?.value || props.node.id
})

const variableType = computed(() => {
  const point = props.node.points.find(p => p.type === 'variableType')
  return point?.text || point?.value || 'text'
})

const currentValue = computed(() => {
  const point = props.node.points.find(p => p.type === 'value')
  return point?.value
})

const units = computed(() => {
  const point = props.node.points.find(p => p.type === 'units')
  return point?.text || point?.value
})

const minValue = computed(() => {
  const point = props.node.points.find(p => p.type === 'minValue')
  return point?.value
})

const maxValue = computed(() => {
  const point = props.node.points.find(p => p.type === 'maxValue')
  return point?.value
})

const precision = computed(() => {
  const point = props.node.points.find(p => p.type === 'precision')
  return point?.value
})

const readOnly = computed(() => {
  const point = props.node.points.find(p => p.type === 'readOnly')
  return point?.value === true
})

const lastUpdateTime = computed(() => {
  const valuePoint = props.node.points.find(p => p.type === 'value')
  return valuePoint?.time || new Date()
})

const displayValue = computed(() => {
  if (variableType.value === 'bool') {
    return currentValue.value ? '开' : '关'
  } else if (variableType.value === 'number') {
    const value = Number(currentValue.value)
    const unit = units.value ? ` ${units.value}` : ''
    return isNaN(value) ? '0' + unit : value.toFixed(precision.value || 2) + unit
  } else {
    return currentValue.value || '空'
  }
})

const valueHistory = computed(() => {
  // 这里应该从历史数据中获取，暂时模拟
  return []
})

const hasHistory = computed(() => {
  return valueHistory.value.length > 0
})

const tags = computed(() => {
  return props.node.points
    .filter(p => p.type === 'tag')
    .map(p => p.value)
})

// 过滤掉特殊的数据点 - 与编辑窗口保持一致
const filteredPoints = computed(() => {
  const excludedTypes = [
    'description', 'disabled', 'debug', 'tag',
    'variableType', 'value', 'units', 'minValue',
    'maxValue', 'precision', 'readOnly',
    'clientServer', 'protocol', 'port', 'uri', 'id', 'baud', 'pollPeriod',
    'groupType',
    'actionType', 'triggerCondition'
  ]
  return props.node.points.filter(p => !excludedTypes.includes(p.type) && p.tombstone !== 1)
})

// 获取类型颜色
const getTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    bool: '#52c41a',
    number: '#1890ff',
    text: '#722ed1'
  }
  return colorMap[type] || '#d9d9d9'
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    bool: '布尔',
    number: '数值',
    text: '文本'
  }
  return textMap[type] || type
}

// 获取值颜色
const getValueColor = (): string => {
  if (variableType.value === 'bool') {
    return currentValue.value ? '#52c41a' : '#ff4d4f'
  }
  return '#1890ff'
}

// 获取历史颜色
const getHistoryColor = (value: any): string => {
  if (variableType.value === 'bool') {
    return value ? 'green' : 'red'
  }
  return 'blue'
}

// 格式化历史值
const formatHistoryValue = (value: any): string => {
  if (variableType.value === 'bool') {
    return value ? '开' : '关'
  } else if (variableType.value === 'number') {
    const num = Number(value)
    const unit = units.value ? ` ${units.value}` : ''
    return isNaN(num) ? '0' + unit : num.toFixed(precision.value || 2) + unit
  }
  return String(value)
}

// 格式化时间
const formatTime = (time: Date): string => {
  return format(time, 'yyyy-MM-dd HH:mm:ss')
}

// 切换展开状态
const toggleExpanded = () => {
  expanded.value = !expanded.value
  emit('update:expanded', expanded.value)
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'edit':
      emit('edit', props.node)
      break
    case 'setValue':
      openSetValueModal()
      break
    case 'delete':
      emit('delete', props.node)
      break
  }
}

// 打开设置值模态框
const openSetValueModal = () => {
  if (readOnly.value) {
    message.warning('此变量为只读，无法设置值')
    return
  }
  
  modalNewValue.value = currentValue.value
  setValueModalVisible.value = true
}

// 处理设置值
const handleSetValue = () => {
  if (readOnly.value) {
    message.warning('此变量为只读，无法设置值')
    return
  }
  
  settingValue.value = true
  
  setTimeout(() => {
    emit('setValue', props.node.id, newValue.value)
    message.success('值设置成功')
    newValue.value = ''
    settingValue.value = false
  }, 1000)
}

// 确认设置值
const confirmSetValue = () => {
  settingValue.value = true
  
  setTimeout(() => {
    emit('setValue', props.node.id, modalNewValue.value)
    message.success('值设置成功')
    setValueModalVisible.value = false
    settingValue.value = false
  }, 1000)
}

// 取消设置值
const cancelSetValue = () => {
  setValueModalVisible.value = false
  modalNewValue.value = currentValue.value
}

// 移除标签
const removeTag = (tag: string) => {
  // 这里应该调用API来移除标签
  console.log('Remove tag:', tag)
}
</script>

<style scoped lang="less">
.node-variable {
  margin-bottom: 16px;
  
  .variable-card {
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #52c41a;
      box-shadow: 0 2px 8px rgba(82, 196, 26, 0.2);
    }
    
    .variable-header {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .variable-icon {
        color: #52c41a;
        font-size: 16px;
      }
      
      .variable-name {
        flex: 1;
        font-weight: 500;
      }
      
      .variable-value {
        .value-tag {
          font-weight: bold;
          font-size: 14px;
        }
      }
    }
    
    .variable-info {
      margin-bottom: 16px;
      
      .ant-statistic {
        text-align: center;
      }
    }
    
    .variable-details {
      .value-setting,
      .variable-config,
      .value-history,
      .data-points,
      .variable-tags {
        margin-bottom: 16px;
        
        h4 {
          margin-bottom: 8px;
          color: #1f2937;
          font-size: 14px;
          font-weight: 600;
        }
      }
      
      .value-history {
        .history-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .history-value {
            font-weight: 500;
          }
          
          .history-time {
            color: #8c8c8c;
            font-size: 12px;
          }
        }
      }
      
      .data-points {
        .ant-table {
          .ant-table-tbody > tr > td {
            padding: 8px 12px;
          }
        }
      }
    }
  }
}
</style>

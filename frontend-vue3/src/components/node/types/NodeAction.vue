<template>
  <div class="node-action">
    <a-card size="small" class="action-card">
      <!-- 动作头部信息 -->
      <template #title>
        <div class="action-header">
          <PlayCircleOutlined class="action-icon" />
          <span class="action-name">{{ actionDescription }}</span>
          <a-tag v-if="actionType" :color="getActionTypeColor(actionType)" size="small">
            {{ getActionTypeText(actionType) }}
          </a-tag>
          <a-tag v-if="actionState" :color="getStateColor(actionState)" size="small">
            {{ getStateText(actionState) }}
          </a-tag>
        </div>
      </template>

      <template #extra>
        <a-space>
          <a-button 
            type="primary" 
            size="small" 
            @click="executeAction"
            :loading="executing"
            :disabled="!canExecute"
          >
            <template #icon>
              <PlayCircleOutlined />
            </template>
            执行
          </a-button>
          <a-button type="text" size="small" @click="toggleExpanded">
            <component :is="expanded ? 'UpOutlined' : 'DownOutlined'" />
          </a-button>
          <a-dropdown>
            <a-button type="text" size="small">
              <MoreOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleMenuClick">
                <a-menu-item key="edit">
                  <EditOutlined />
                  编辑
                </a-menu-item>
                <a-menu-item key="test">
                  <BugOutlined />
                  测试
                </a-menu-item>
                <a-menu-item key="logs">
                  <FileTextOutlined />
                  日志
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="delete" danger>
                  <DeleteOutlined />
                  删除
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </template>

      <!-- 动作基本信息 -->
      <div class="action-info">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic
              title="执行状态"
              :value="getStateText(actionState)"
              :value-style="{ color: getStateColor(actionState) }"
            >
              <template #prefix>
                <component :is="getStateIcon(actionState)" />
              </template>
            </a-statistic>
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="执行次数"
              :value="executionCount"
              suffix="次"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="成功率"
              :value="successRate"
              :value-style="{ color: successRate > 90 ? '#52c41a' : '#ff4d4f' }"
              suffix="%"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="最后执行"
              :value="lastExecutionTime"
              format="HH:mm:ss"
            />
          </a-col>
        </a-row>
      </div>

      <!-- 展开的详细信息 -->
      <div v-if="expanded" class="action-details">
        <a-divider />
        
        <!-- 动作配置 -->
        <div class="action-config">
          <h4>动作配置</h4>
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="动作类型">
              {{ getActionTypeText(actionType) }}
            </a-descriptions-item>
            <a-descriptions-item label="触发条件">
              {{ triggerCondition || 'N/A' }}
            </a-descriptions-item>
            <a-descriptions-item label="超时时间">
              {{ timeout ? `${timeout}ms` : 'N/A' }}
            </a-descriptions-item>
            <a-descriptions-item label="重试次数">
              {{ retryCount || 0 }}
            </a-descriptions-item>
            <a-descriptions-item label="优先级">
              {{ priority || 'normal' }}
            </a-descriptions-item>
            <a-descriptions-item label="是否启用">
              <a-tag :color="enabled ? 'green' : 'red'">
                {{ enabled ? '是' : '否' }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 动作内容 -->
        <div class="action-content">
          <h4>动作内容</h4>
          <template v-if="actionType === 'command'">
            <a-form layout="vertical" size="small">
              <a-form-item label="命令">
                <a-input :value="command" readonly />
              </a-form-item>
              <a-form-item v-if="commandArgs" label="参数">
                <a-input :value="commandArgs" readonly />
              </a-form-item>
            </a-form>
          </template>
          <template v-else-if="actionType === 'script'">
            <a-form layout="vertical" size="small">
              <a-form-item label="脚本内容">
                <a-textarea :value="scriptContent" :rows="4" readonly />
              </a-form-item>
            </a-form>
          </template>
          <template v-else-if="actionType === 'notification'">
            <a-form layout="vertical" size="small">
              <a-form-item label="通知类型">
                <a-input :value="notificationType" readonly />
              </a-form-item>
              <a-form-item label="通知内容">
                <a-textarea :value="notificationContent" :rows="3" readonly />
              </a-form-item>
            </a-form>
          </template>
        </div>

        <!-- 执行历史 -->
        <div v-if="executionHistory.length > 0" class="execution-history">
          <h4>执行历史</h4>
          <a-timeline size="small">
            <a-timeline-item 
              v-for="(item, index) in executionHistory.slice(0, 5)" 
              :key="index"
              :color="getHistoryColor(item.status)"
            >
              <div class="history-item">
                <div class="history-header">
                  <span class="history-status">{{ getHistoryStatusText(item.status) }}</span>
                  <span class="history-time">{{ formatTime(item.time) }}</span>
                </div>
                <div v-if="item.message" class="history-message">
                  {{ item.message }}
                </div>
                <div v-if="item.duration" class="history-duration">
                  耗时: {{ item.duration }}ms
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
          <a-button v-if="executionHistory.length > 5" type="link" size="small" @click="showAllHistory">
            查看全部历史 ({{ executionHistory.length }})
          </a-button>
        </div>

        <!-- 数据点列表 -->
        <div class="data-points">
          <h4>数据点 ({{ filteredPoints.length }})</h4>
          <a-table
            :columns="pointColumns"
            :data-source="filteredPoints"
            :pagination="false"
            size="small"
            row-key="type"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'value'">
                <span v-if="typeof record.value === 'boolean'">
                  <a-tag :color="record.value ? 'green' : 'red'">
                    {{ record.value ? '是' : '否' }}
                  </a-tag>
                </span>
                <span v-else>{{ record.text || record.value }}</span>
              </template>
              <template v-else-if="column.key === 'time'">
                {{ formatTime(record.time) }}
              </template>
            </template>
          </a-table>
        </div>

        <!-- 标签管理 -->
        <div v-if="tags.length > 0" class="action-tags">
          <h4>标签</h4>
          <a-space wrap>
            <a-tag v-for="tag in tags" :key="tag" closable @close="removeTag(tag)">
              {{ tag }}
            </a-tag>
          </a-space>
        </div>
      </div>
    </a-card>

    <!-- 执行确认模态框 -->
    <a-modal
      v-model:open="executeModalVisible"
      title="确认执行动作"
      @ok="confirmExecute"
      @cancel="cancelExecute"
      :confirm-loading="executing"
    >
      <div class="execute-confirm">
        <a-alert
          message="确认执行"
          :description="`您确定要执行动作 "${actionDescription}" 吗？`"
          type="warning"
          show-icon
        />
        <div v-if="actionType === 'command'" class="execute-details">
          <p><strong>命令:</strong> {{ command }}</p>
          <p v-if="commandArgs"><strong>参数:</strong> {{ commandArgs }}</p>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { format } from 'date-fns'
import { message } from 'ant-design-vue'
import {
  PlayCircleOutlined,
  UpOutlined,
  DownOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  BugOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'
import type { Node } from '@/types/node'

interface ExecutionHistoryItem {
  time: Date
  status: 'success' | 'failed' | 'timeout'
  message?: string
  duration?: number
}

interface Props {
  node: Node
  expanded?: boolean
}

interface Emits {
  (e: 'edit', node: Node): void
  (e: 'delete', node: Node): void
  (e: 'execute', node: Node): void
  (e: 'test', node: Node): void
  (e: 'showLogs', node: Node): void
  (e: 'update:expanded', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  expanded: false
})

const emit = defineEmits<Emits>()

// 状态
const expanded = ref(props.expanded)
const executing = ref(false)
const executeModalVisible = ref(false)

// 模拟执行历史数据
const executionHistory = ref<ExecutionHistoryItem[]>([
  {
    time: new Date(Date.now() - 3600000),
    status: 'success',
    message: '执行成功',
    duration: 1200
  },
  {
    time: new Date(Date.now() - 7200000),
    status: 'failed',
    message: '连接超时',
    duration: 5000
  }
])

// 数据点表格列
const pointColumns = [
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: '键', dataIndex: 'key', key: 'key', width: 100 },
  { title: '值', dataIndex: 'value', key: 'value', width: 150 },
  { title: '时间', dataIndex: 'time', key: 'time', width: 160 }
]

// 计算属性
const actionDescription = computed(() => {
  const descPoint = props.node.points.find(p => p.type === 'description')
  return descPoint?.text || descPoint?.value || props.node.id
})

const actionType = computed(() => {
  const point = props.node.points.find(p => p.type === 'actionType')
  return point?.text || point?.value || 'command'
})

const actionState = computed(() => {
  const point = props.node.points.find(p => p.type === 'state')
  return point?.text || point?.value || 'idle'
})

const triggerCondition = computed(() => {
  const point = props.node.points.find(p => p.type === 'triggerCondition')
  return point?.text || point?.value
})

const timeout = computed(() => {
  const point = props.node.points.find(p => p.type === 'timeout')
  return point?.value
})

const retryCount = computed(() => {
  const point = props.node.points.find(p => p.type === 'retryCount')
  return point?.value
})

const priority = computed(() => {
  const point = props.node.points.find(p => p.type === 'priority')
  return point?.text || point?.value
})

const enabled = computed(() => {
  const point = props.node.points.find(p => p.type === 'enabled')
  return point?.value !== false
})

const command = computed(() => {
  const point = props.node.points.find(p => p.type === 'command')
  return point?.text || point?.value
})

const commandArgs = computed(() => {
  const point = props.node.points.find(p => p.type === 'commandArgs')
  return point?.text || point?.value
})

const scriptContent = computed(() => {
  const point = props.node.points.find(p => p.type === 'scriptContent')
  return point?.text || point?.value
})

const notificationType = computed(() => {
  const point = props.node.points.find(p => p.type === 'notificationType')
  return point?.text || point?.value
})

const notificationContent = computed(() => {
  const point = props.node.points.find(p => p.type === 'notificationContent')
  return point?.text || point?.value
})

const executionCount = computed(() => {
  const point = props.node.points.find(p => p.type === 'executionCount')
  return point?.value || 0
})

const successCount = computed(() => {
  return executionHistory.value.filter(item => item.status === 'success').length
})

const successRate = computed(() => {
  if (executionHistory.value.length === 0) return 100
  return Math.round((successCount.value / executionHistory.value.length) * 100)
})

const lastExecutionTime = computed(() => {
  if (executionHistory.value.length === 0) return new Date()
  return executionHistory.value[0].time
})

const canExecute = computed(() => {
  return enabled.value && actionState.value !== 'executing'
})

const tags = computed(() => {
  return props.node.points
    .filter(p => p.type === 'tag')
    .map(p => p.value)
})

// 过滤掉特殊的数据点 - 与编辑窗口保持一致
const filteredPoints = computed(() => {
  const excludedTypes = [
    'description', 'disabled', 'debug', 'tag',
    'actionType', 'state', 'triggerCondition', 'timeout',
    'retryCount', 'priority', 'enabled', 'command', 'commandArgs',
    'scriptContent', 'notificationType', 'notificationContent',
    'executionCount',
    'variableType', 'value', 'units',
    'groupType'
  ]
  return props.node.points.filter(p => !excludedTypes.includes(p.type) && p.tombstone !== 1)
})

// 获取动作类型颜色
const getActionTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    command: '#1890ff',
    script: '#52c41a',
    notification: '#fa8c16'
  }
  return colorMap[type] || '#d9d9d9'
}

// 获取动作类型文本
const getActionTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    command: '命令',
    script: '脚本',
    notification: '通知'
  }
  return textMap[type] || type
}

// 获取状态颜色
const getStateColor = (state: string): string => {
  const colorMap: Record<string, string> = {
    idle: '#d9d9d9',
    executing: '#1890ff',
    success: '#52c41a',
    failed: '#ff4d4f'
  }
  return colorMap[state] || '#d9d9d9'
}

// 获取状态文本
const getStateText = (state: string): string => {
  const textMap: Record<string, string> = {
    idle: '空闲',
    executing: '执行中',
    success: '成功',
    failed: '失败'
  }
  return textMap[state] || state
}

// 获取状态图标
const getStateIcon = (state: string) => {
  const iconMap: Record<string, any> = {
    idle: CheckCircleOutlined,
    executing: LoadingOutlined,
    success: CheckCircleOutlined,
    failed: CloseCircleOutlined
  }
  return iconMap[state] || CheckCircleOutlined
}

// 获取历史颜色
const getHistoryColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    success: 'green',
    failed: 'red',
    timeout: 'orange'
  }
  return colorMap[status] || 'blue'
}

// 获取历史状态文本
const getHistoryStatusText = (status: string): string => {
  const textMap: Record<string, string> = {
    success: '成功',
    failed: '失败',
    timeout: '超时'
  }
  return textMap[status] || status
}

// 格式化时间
const formatTime = (time: Date): string => {
  return format(time, 'yyyy-MM-dd HH:mm:ss')
}

// 切换展开状态
const toggleExpanded = () => {
  expanded.value = !expanded.value
  emit('update:expanded', expanded.value)
}

// 执行动作
const executeAction = () => {
  if (!canExecute.value) {
    message.warning('动作当前无法执行')
    return
  }
  
  executeModalVisible.value = true
}

// 确认执行
const confirmExecute = () => {
  executing.value = true
  
  // 模拟执行
  setTimeout(() => {
    const success = Math.random() > 0.2 // 80% 成功率
    const newHistoryItem: ExecutionHistoryItem = {
      time: new Date(),
      status: success ? 'success' : 'failed',
      message: success ? '执行成功' : '执行失败',
      duration: Math.floor(Math.random() * 3000) + 500
    }
    
    executionHistory.value.unshift(newHistoryItem)
    
    if (success) {
      message.success('动作执行成功')
    } else {
      message.error('动作执行失败')
    }
    
    executing.value = false
    executeModalVisible.value = false
    
    emit('execute', props.node)
  }, 2000)
}

// 取消执行
const cancelExecute = () => {
  executeModalVisible.value = false
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'edit':
      emit('edit', props.node)
      break
    case 'test':
      emit('test', props.node)
      break
    case 'logs':
      emit('showLogs', props.node)
      break
    case 'delete':
      emit('delete', props.node)
      break
  }
}

// 显示全部历史
const showAllHistory = () => {
  // 这里可以打开一个模态框显示全部历史
  message.info('显示全部执行历史')
}

// 移除标签
const removeTag = (tag: string) => {
  // 这里应该调用API来移除标签
  console.log('Remove tag:', tag)
}
</script>

<style scoped lang="less">
.node-action {
  margin-bottom: 16px;
  
  .action-card {
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #eb2f96;
      box-shadow: 0 2px 8px rgba(235, 47, 150, 0.2);
    }
    
    .action-header {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .action-icon {
        color: #eb2f96;
        font-size: 16px;
      }
      
      .action-name {
        flex: 1;
        font-weight: 500;
      }
    }
    
    .action-info {
      margin-bottom: 16px;
      
      .ant-statistic {
        text-align: center;
      }
    }
    
    .action-details {
      .action-config,
      .action-content,
      .execution-history,
      .data-points,
      .action-tags {
        margin-bottom: 16px;
        
        h4 {
          margin-bottom: 8px;
          color: #1f2937;
          font-size: 14px;
          font-weight: 600;
        }
      }
      
      .execution-history {
        .history-item {
          .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
            
            .history-status {
              font-weight: 500;
            }
            
            .history-time {
              color: #8c8c8c;
              font-size: 12px;
            }
          }
          
          .history-message {
            color: #666;
            font-size: 12px;
            margin-bottom: 2px;
          }
          
          .history-duration {
            color: #999;
            font-size: 11px;
          }
        }
      }
      
      .data-points {
        .ant-table {
          .ant-table-tbody > tr > td {
            padding: 8px 12px;
          }
        }
      }
    }
  }
  
  .execute-confirm {
    .execute-details {
      margin-top: 16px;
      padding: 12px;
      background: #fafafa;
      border-radius: 6px;
      
      p {
        margin: 4px 0;
        font-size: 13px;
      }
    }
  }
}
</style>

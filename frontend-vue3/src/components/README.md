# 组件库文档

## 通用组件 (Common Components)

### Button 按钮组件

基于 Ant Design Vue Button 的封装，提供额外的变体样式。

#### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| type | 'primary' \| 'default' \| 'dashed' \| 'text' \| 'link' | 'default' | 按钮类型 |
| size | 'large' \| 'middle' \| 'small' | 'middle' | 按钮尺寸 |
| loading | boolean | false | 加载状态 |
| disabled | boolean | false | 禁用状态 |
| block | boolean | false | 块级按钮 |
| ghost | boolean | false | 幽灵按钮 |
| danger | boolean | false | 危险按钮 |
| shape | 'default' \| 'circle' \| 'round' | 'default' | 按钮形状 |
| htmlType | 'button' \| 'submit' \| 'reset' | 'button' | HTML 类型 |
| icon | Component | - | 图标组件 |
| variant | 'success' \| 'warning' \| 'error' \| 'info' | - | 变体样式 |

#### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| click | 点击事件 | (event: MouseEvent) |

#### 使用示例

```vue
<template>
  <Button type="primary" variant="success" @click="handleClick">
    成功按钮
  </Button>
</template>
```

### Form 表单组件

基于 Ant Design Vue Form 的封装，提供统一的表单样式。

#### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| model | Record<string, any> | - | 表单数据对象 |
| rules | Record<string, Rule \| Rule[]> | - | 表单验证规则 |
| layout | 'horizontal' \| 'vertical' \| 'inline' | 'vertical' | 表单布局 |
| labelCol | FormProps['labelCol'] | - | 标签列配置 |
| wrapperCol | FormProps['wrapperCol'] | - | 输入控件列配置 |
| labelAlign | 'left' \| 'right' | 'right' | 标签对齐方式 |
| colon | boolean | true | 是否显示冒号 |
| validateTrigger | string \| string[] | 'change' | 验证触发方式 |
| size | 'small' \| 'middle' \| 'large' | 'middle' | 表单尺寸 |
| disabled | boolean | false | 禁用状态 |

#### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| finish | 表单提交成功 | (values: Record<string, any>) |
| finishFailed | 表单提交失败 | (errorInfo: any) |
| valuesChange | 表单值变化 | (changedValues: Record<string, any>, allValues: Record<string, any>) |

### Icon 图标组件

基于 Ant Design Icons Vue 的封装，提供统一的图标使用方式。

#### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| name | string | - | 图标名称 |
| size | number \| string | 16 | 图标尺寸 |
| color | string | - | 图标颜色 |
| spin | boolean | false | 旋转动画 |
| rotate | number | 0 | 旋转角度 |
| clickable | boolean | false | 可点击状态 |

#### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| click | 点击事件 | (event: MouseEvent) |

#### 可用图标

- home, user, setting, menu, search, plus, edit, delete, save, close, check
- exclamation, info, question, warning, loading, reload, download, upload
- eye, eye-invisible, copy, share, printer, mail, phone, calendar, clock
- environment, global, wifi, database, cloud, security, bug, tool, api, code
- file, folder, picture, video, sound, bell, heart, star, like, dislike
- comment, message, send, logout, login, lock, unlock, key, shield, safety
- fire, thunderbolt, bulb, rocket, trophy, gift, crown, diamond

#### 使用示例

```vue
<template>
  <Icon name="home" size="20" color="#1890ff" clickable @click="handleClick" />
</template>
```

## 设备组件 (Node Components)

设备组件将在第二阶段开发，用于管理各种IoT设备类型。

## UI组件 (UI Components)

UI组件将在后续阶段开发，包括复杂的业务组件。

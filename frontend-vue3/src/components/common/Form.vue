<template>
  <a-form
    :model="model"
    :rules="rules"
    :layout="layout"
    :label-col="labelCol"
    :wrapper-col="wrapperCol"
    :label-align="labelAlign"
    :colon="colon"
    :validate-trigger="validateTrigger"
    :class="formClass"
    @finish="handleFinish"
    @finishFailed="handleFinishFailed"
    @valuesChange="handleValuesChange"
  >
    <slot />
  </a-form>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { FormProps, Rule } from 'ant-design-vue/es/form'

interface Props {
  model: Record<string, any>
  rules?: Record<string, Rule | Rule[]>
  layout?: 'horizontal' | 'vertical' | 'inline'
  labelCol?: FormProps['labelCol']
  wrapperCol?: FormProps['wrapperCol']
  labelAlign?: 'left' | 'right'
  colon?: boolean
  validateTrigger?: string | string[]
  size?: 'small' | 'middle' | 'large'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  layout: 'vertical',
  labelAlign: 'right',
  colon: true,
  validateTrigger: 'change',
  size: 'middle',
  disabled: false
})

const emit = defineEmits<{
  finish: [values: Record<string, any>]
  finishFailed: [errorInfo: any]
  valuesChange: [changedValues: Record<string, any>, allValues: Record<string, any>]
}>()

// 计算表单样式类
const formClass = computed(() => {
  const classes = ['siot-form']
  
  if (props.size) {
    classes.push(`siot-form--${props.size}`)
  }
  
  if (props.disabled) {
    classes.push('siot-form--disabled')
  }
  
  return classes
})

// 处理表单提交成功
const handleFinish = (values: Record<string, any>) => {
  emit('finish', values)
}

// 处理表单提交失败
const handleFinishFailed = (errorInfo: any) => {
  emit('finishFailed', errorInfo)
}

// 处理表单值变化
const handleValuesChange = (changedValues: Record<string, any>, allValues: Record<string, any>) => {
  emit('valuesChange', changedValues, allValues)
}
</script>

<style scoped lang="less">
.siot-form {
  &--small {
    .ant-form-item {
      margin-bottom: @padding-sm;
    }
  }
  
  &--large {
    .ant-form-item {
      margin-bottom: @padding-lg;
    }
  }
  
  &--disabled {
    .ant-form-item {
      opacity: 0.6;
      pointer-events: none;
    }
  }
}

// 自定义表单项样式
:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-form-item-required) {
  &::before {
    color: @error-color;
  }
}

:deep(.ant-form-item-explain-error) {
  font-size: @font-size-sm;
}
</style>

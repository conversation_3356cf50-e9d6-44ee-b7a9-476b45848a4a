<template>
  <component
    :is="iconComponent"
    :class="iconClass"
    :style="iconStyle"
    @click="handleClick"
  />
</template>

<script setup lang="ts">
import { computed, type Component } from 'vue'
import {
  // 常用图标
  HomeOutlined,
  UserOutlined,
  SettingOutlined,
  MenuOutlined,
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SaveOutlined,
  CloseOutlined,
  CheckOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  QuestionCircleOutlined,
  WarningOutlined,
  LoadingOutlined,
  ReloadOutlined,
  DownloadOutlined,
  UploadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  CopyOutlined,
  ShareAltOutlined,
  PrinterOutlined,
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  GlobalOutlined,
  WifiOutlined,
  DatabaseOutlined,
  CloudOutlined,
  SecurityScanOutlined,
  BugOutlined,
  ToolOutlined,
  ApiOutlined,
  CodeOutlined,
  FileOutlined,
  FolderOutlined,
  PictureOutlined,
  <PERSON>CameraOutlined,
  SoundOutlined,
  BellOutlined,
  <PERSON>Outlined,
  <PERSON>Outlined,
  LikeOutlined,
  DislikeOutlined,
  CommentOutlined,
  MessageOutlined,
  SendOutlined,
  <PERSON>goutOutlined,
  LoginOutlined,
  LockOutlined,
  UnlockOutlined,
  KeyOutlined,
  SafetyOutlined,
  FireOutlined,
  ThunderboltOutlined,
  BulbOutlined,
  RocketOutlined,
  TrophyOutlined,
  GiftOutlined,
  CrownOutlined
} from '@ant-design/icons-vue'

// 图标映射
const iconMap: Record<string, Component> = {
  home: HomeOutlined,
  user: UserOutlined,
  setting: SettingOutlined,
  menu: MenuOutlined,
  search: SearchOutlined,
  plus: PlusOutlined,
  edit: EditOutlined,
  delete: DeleteOutlined,
  save: SaveOutlined,
  close: CloseOutlined,
  check: CheckOutlined,
  exclamation: ExclamationCircleOutlined,
  info: InfoCircleOutlined,
  question: QuestionCircleOutlined,
  warning: WarningOutlined,
  loading: LoadingOutlined,
  reload: ReloadOutlined,
  download: DownloadOutlined,
  upload: UploadOutlined,
  eye: EyeOutlined,
  'eye-invisible': EyeInvisibleOutlined,
  copy: CopyOutlined,
  share: ShareAltOutlined,
  printer: PrinterOutlined,
  mail: MailOutlined,
  phone: PhoneOutlined,
  calendar: CalendarOutlined,
  clock: ClockCircleOutlined,
  environment: EnvironmentOutlined,
  global: GlobalOutlined,
  wifi: WifiOutlined,
  database: DatabaseOutlined,
  cloud: CloudOutlined,
  security: SecurityScanOutlined,
  bug: BugOutlined,
  tool: ToolOutlined,
  api: ApiOutlined,
  code: CodeOutlined,
  file: FileOutlined,
  folder: FolderOutlined,
  picture: PictureOutlined,
  video: VideoCameraOutlined,
  sound: SoundOutlined,
  bell: BellOutlined,
  heart: HeartOutlined,
  star: StarOutlined,
  like: LikeOutlined,
  dislike: DislikeOutlined,
  comment: CommentOutlined,
  message: MessageOutlined,
  send: SendOutlined,
  logout: LogoutOutlined,
  login: LoginOutlined,
  lock: LockOutlined,
  unlock: UnlockOutlined,
  key: KeyOutlined,
  safety: SafetyOutlined,
  fire: FireOutlined,
  thunderbolt: ThunderboltOutlined,
  bulb: BulbOutlined,
  rocket: RocketOutlined,
  trophy: TrophyOutlined,
  gift: GiftOutlined,
  crown: CrownOutlined
}

interface Props {
  name: string
  size?: number | string
  color?: string
  spin?: boolean
  rotate?: number
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 16,
  spin: false,
  rotate: 0,
  clickable: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

// 获取图标组件
const iconComponent = computed(() => {
  return iconMap[props.name] || InfoCircleOutlined
})

// 计算图标样式类
const iconClass = computed(() => {
  const classes = ['siot-icon']
  
  if (props.spin) {
    classes.push('siot-icon--spin')
  }
  
  if (props.clickable) {
    classes.push('siot-icon--clickable')
  }
  
  return classes
})

// 计算图标样式
const iconStyle = computed(() => {
  const style: Record<string, any> = {}
  
  if (props.size) {
    style.fontSize = typeof props.size === 'number' ? `${props.size}px` : props.size
  }
  
  if (props.color) {
    style.color = props.color
  }
  
  if (props.rotate) {
    style.transform = `rotate(${props.rotate}deg)`
  }
  
  return style
})

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  if (props.clickable) {
    emit('click', event)
  }
}
</script>

<style scoped lang="less">
.siot-icon {
  display: inline-block;
  transition: all 0.3s;
  
  &--spin {
    animation: spin 1s linear infinite;
  }
  
  &--clickable {
    cursor: pointer;
    
    &:hover {
      opacity: 0.8;
      transform: scale(1.1);
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>

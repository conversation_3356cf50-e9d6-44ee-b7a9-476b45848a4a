<template>
  <a-button
    :type="type"
    :size="size"
    :loading="loading"
    :disabled="disabled"
    :block="block"
    :ghost="ghost"
    :danger="danger"
    :shape="shape"
    :html-type="htmlType"
    :class="buttonClass"
    @click="handleClick"
  >
    <template #icon v-if="icon">
      <component :is="icon" />
    </template>
    <slot />
  </a-button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Component } from 'vue'

interface Props {
  type?: 'primary' | 'default' | 'dashed' | 'text' | 'link'
  size?: 'large' | 'middle' | 'small'
  loading?: boolean
  disabled?: boolean
  block?: boolean
  ghost?: boolean
  danger?: boolean
  shape?: 'default' | 'circle' | 'round'
  htmlType?: 'button' | 'submit' | 'reset'
  icon?: Component
  variant?: 'success' | 'warning' | 'error' | 'info'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  size: 'middle',
  loading: false,
  disabled: false,
  block: false,
  ghost: false,
  danger: false,
  shape: 'default',
  htmlType: 'button'
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

// 计算按钮样式类
const buttonClass = computed(() => {
  const classes = ['siot-button']
  
  if (props.variant) {
    classes.push(`siot-button--${props.variant}`)
  }
  
  return classes
})

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped lang="less">
.siot-button {
  &--success {
    &.ant-btn-primary {
      background-color: @success-color;
      border-color: @success-color;
      
      &:hover, &:focus {
        background-color: lighten(@success-color, 10%);
        border-color: lighten(@success-color, 10%);
      }
    }
  }
  
  &--warning {
    &.ant-btn-primary {
      background-color: @warning-color;
      border-color: @warning-color;
      
      &:hover, &:focus {
        background-color: lighten(@warning-color, 10%);
        border-color: lighten(@warning-color, 10%);
      }
    }
  }
  
  &--error {
    &.ant-btn-primary {
      background-color: @error-color;
      border-color: @error-color;
      
      &:hover, &:focus {
        background-color: lighten(@error-color, 10%);
        border-color: lighten(@error-color, 10%);
      }
    }
  }
  
  &--info {
    &.ant-btn-primary {
      background-color: @info-color;
      border-color: @info-color;
      
      &:hover, &:focus {
        background-color: lighten(@info-color, 10%);
        border-color: lighten(@info-color, 10%);
      }
    }
  }
}
</style>

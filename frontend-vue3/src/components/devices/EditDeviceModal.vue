<template>
  <a-modal
    v-model:open="visible"
    title="编辑设备"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirm-loading="loading"
    width="900px"
  >
    <a-tabs v-model:activeKey="activeTab" type="card">
      <!-- 基本信息标签页 -->
      <a-tab-pane key="basic" tab="基本信息">
        <a-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          layout="vertical"
          @finish="handleSubmit"
        >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="设备ID">
            <a-input :value="node?.id" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="设备类型">
            <a-select :value="node?.type" disabled>
              <a-select-option value="device">设备</a-select-option>
              <a-select-option value="modbus">Modbus</a-select-option>
              <a-select-option value="variable">变量</a-select-option>
              <a-select-option value="group">分组</a-select-option>
              <a-select-option value="action">动作</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="父节点" name="parent">
        <a-tree-select
          v-model:value="formData.parent"
          :tree-data="parentOptions"
          placeholder="请选择父节点"
          tree-default-expand-all
          allow-clear
        />
      </a-form-item>

      <a-form-item label="设备描述" name="description">
        <a-input 
          v-model:value="formData.description" 
          placeholder="请输入设备描述"
        />
      </a-form-item>

      <!-- 根据设备类型显示不同的配置项 -->
      <div v-if="node?.type" class="type-specific-config">
        <a-divider>{{ getTypeText(node.type) }}配置</a-divider>
        
        <!-- Modbus类型配置 -->
        <template v-if="node.type === 'modbus'">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="客户端/服务端" name="clientServer">
                <a-select v-model:value="formData.clientServer" placeholder="选择模式">
                  <a-select-option value="client">客户端</a-select-option>
                  <a-select-option value="server">服务端</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="协议类型" name="modbusProtocol">
                <a-select v-model:value="formData.modbusProtocol" placeholder="选择协议">
                  <a-select-option value="RTU">RTU</a-select-option>
                  <a-select-option value="TCP">TCP</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="端口/URI" name="portUri">
                <a-input 
                  v-model:value="formData.portUri" 
                  :placeholder="getPortPlaceholder()"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="设备ID" name="deviceId">
                <a-input-number 
                  v-model:value="formData.deviceId" 
                  :min="1" 
                  :max="255"
                  placeholder="设备ID"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="波特率" name="baud">
                <a-select v-model:value="formData.baud" placeholder="选择波特率">
                  <a-select-option value="9600">9600</a-select-option>
                  <a-select-option value="19200">19200</a-select-option>
                  <a-select-option value="38400">38400</a-select-option>
                  <a-select-option value="115200">115200</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="轮询周期(ms)" name="pollPeriod">
                <a-input-number 
                  v-model:value="formData.pollPeriod" 
                  :min="100"
                  placeholder="轮询周期"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </template>

        <!-- 变量类型配置 -->
        <template v-if="node.type === 'variable'">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="变量类型" name="variableType">
                <a-select v-model:value="formData.variableType" placeholder="选择变量类型">
                  <a-select-option value="number">数值</a-select-option>
                  <a-select-option value="text">文本</a-select-option>
                  <a-select-option value="bool">布尔</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="当前值" name="currentValue">
                <a-input 
                  v-model:value="formData.currentValue" 
                  placeholder="当前值"
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item label="单位" name="units">
            <a-input 
              v-model:value="formData.units" 
              placeholder="单位（可选）"
            />
          </a-form-item>
        </template>

        <!-- 分组类型配置 -->
        <template v-if="node.type === 'group'">
          <a-form-item label="分组类型" name="groupType">
            <a-select v-model:value="formData.groupType" placeholder="选择分组类型">
              <a-select-option value="logical">逻辑分组</a-select-option>
              <a-select-option value="physical">物理分组</a-select-option>
              <a-select-option value="functional">功能分组</a-select-option>
            </a-select>
          </a-form-item>
        </template>

        <!-- 动作类型配置 -->
        <template v-if="node.type === 'action'">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="动作类型" name="actionType">
                <a-select v-model:value="formData.actionType" placeholder="选择动作类型">
                  <a-select-option value="command">命令</a-select-option>
                  <a-select-option value="script">脚本</a-select-option>
                  <a-select-option value="notification">通知</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="触发条件" name="triggerCondition">
                <a-input 
                  v-model:value="formData.triggerCondition" 
                  placeholder="触发条件"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </template>
      </div>

      <!-- 标签配置 -->
      <a-form-item label="标签">
        <a-select
          v-model:value="formData.tags"
          mode="tags"
          placeholder="添加标签"
          :options="tagOptions"
        />
      </a-form-item>

      <!-- 是否禁用 -->
      <a-form-item name="disabled">
        <a-checkbox v-model:checked="formData.disabled">
          禁用此设备
        </a-checkbox>
      </a-form-item>

      <!-- 调试级别 -->
      <a-form-item label="调试级别" name="debugLevel">
        <a-slider
          v-model:value="formData.debugLevel"
          :min="0"
          :max="9"
          :marks="{ 0: '0', 3: '3', 6: '6', 9: '9' }"
        />
      </a-form-item>
        </a-form>
      </a-tab-pane>

      <!-- 数据点管理标签页 -->
      <a-tab-pane key="points" tab="数据点管理">
        <div class="points-management">
          <div class="points-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
            <h4 style="margin: 0;">数据点列表</h4>
            <a-button type="dashed" @click="showAddPointModal">
              <template #icon>
                <PlusOutlined />
              </template>
              添加数据点
            </a-button>
          </div>

          <a-table
            :columns="pointColumns"
            :data-source="editablePoints"
            :pagination="false"
            size="small"
            row-key="key"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'type'">
                <a-input v-model:value="record.type" size="small" @change="markPointsChanged" />
              </template>
              <template v-else-if="column.key === 'key'">
                <a-input v-model:value="record.key" size="small" @change="markPointsChanged" />
              </template>
              <template v-else-if="column.key === 'value'">
                <a-input-number v-model:value="record.value" size="small" style="width: 100%" @change="markPointsChanged" />
              </template>
              <template v-else-if="column.key === 'text'">
                <a-input v-model:value="record.text" size="small" @change="markPointsChanged" />
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-button type="text" danger size="small" @click="removePoint(index)">
                  <template #icon>
                    <DeleteOutlined />
                  </template>
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- 添加数据点模态框 -->
    <a-modal
      v-model:open="addPointModalVisible"
      title="添加数据点"
      @ok="handleAddPoint"
      @cancel="cancelAddPoint"
    >
      <a-form layout="vertical" :model="newPoint">
        <a-form-item label="数据点类型" required>
          <a-input v-model:value="newPoint.type" placeholder="如: temperature, humidity" />
        </a-form-item>
        <a-form-item label="数据点键值" required>
          <a-input v-model:value="newPoint.key" placeholder="如: 0, 1, 2" />
        </a-form-item>
        <a-form-item label="数值">
          <a-input-number v-model:value="newPoint.value" style="width: 100%" />
        </a-form-item>
        <a-form-item label="文本描述">
          <a-input v-model:value="newPoint.text" placeholder="数据点描述" />
        </a-form-item>
      </a-form>
    </a-modal>

  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  DeleteOutlined,
  TagOutlined
} from '@ant-design/icons-vue'
import { useNodeStore } from '@/stores/node'
import type { Node, UpdateNodeRequest, UpdateNodeSendRequest, Point } from '@/types/node'

interface Props {
  open: boolean
  node: Node | null
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const nodeStore = useNodeStore()

// 状态
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const loading = ref(false)
const formRef = ref()
const activeTab = ref('basic')

// 数据点管理相关状态
const editablePoints = ref<Point[]>([])
const addPointModalVisible = ref(false)

// 新数据点
const newPoint = ref({
  type: '',
  key: '',
  value: undefined as number | undefined,
  text: ''
})

// 新标签
const newTag = ref({
  name: ''
})

// 数据点表格列
const pointColumns = [
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: '键值', dataIndex: 'key', key: 'key', width: 80 },
  { title: '数值', dataIndex: 'value', key: 'value', width: 100 },
  { title: '文本', dataIndex: 'text', key: 'text' },
  { title: '操作', key: 'actions', width: 80 }
]

// 表单数据
const formData = ref({
  parent: '',
  description: '',
  // Modbus特定字段
  clientServer: '',
  modbusProtocol: '',
  portUri: '',
  deviceId: 1,
  baud: '',
  pollPeriod: 1000,
  // 变量特定字段
  variableType: '',
  currentValue: '',
  units: '',
  // 分组特定字段
  groupType: '',
  // 动作特定字段
  actionType: '',
  triggerCondition: '',
  // 通用字段
  tags: [] as string[],
  disabled: false,
  debugLevel: 0
})

// 表单验证规则
const rules = {
  description: [
    { required: true, message: '请输入设备描述', trigger: 'blur' }
  ]
}

// 父节点选项
const parentOptions = computed(() => {
  const nodes = nodeStore.nodes.data || []
  
  const buildOptions = (nodeList: any[]): any[] => {
    return nodeList
      .filter(node => ['group', 'device'].includes(node.type) && node.id !== props.node?.id)
      .map(node => ({
        title: getNodeDescription(node),
        value: node.id,
        key: node.id
      }))
  }
  
  return [
    { title: '根节点', value: '', key: 'root' },
    ...buildOptions(nodes)
  ]
})

// 标签选项
const tagOptions = [
  { label: '生产', value: 'production' },
  { label: '测试', value: 'test' },
  { label: '重要', value: 'important' },
  { label: '监控', value: 'monitoring' }
]

// 获取节点描述
const getNodeDescription = (node: any): string => {
  const descPoint = node.points?.find((p: any) => p.type === 'description')
  return descPoint?.text || descPoint?.value || node.id
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    device: '设备',
    modbus: 'Modbus',
    variable: '变量',
    group: '分组',
    action: '动作'
  }
  return textMap[type] || type
}

// 获取端口占位符
const getPortPlaceholder = (): string => {
  if (formData.value.modbusProtocol === 'RTU') {
    return '/dev/ttyUSB0'
  } else if (formData.value.modbusProtocol === 'TCP') {
    return formData.value.clientServer === 'server' ? '502' : '*************:502'
  }
  return '端口或URI'
}

// 从节点数据初始化表单
const initFormData = () => {
  if (!props.node) return

  const node = props.node

  // 基础信息
  formData.value.parent = node.parent
  formData.value.description = getPointValue(node, 'description', '')
  formData.value.disabled = getPointValue(node, 'disabled', false)
  formData.value.debugLevel = getPointValue(node, 'debug', 0)

  // 初始化数据点 - 排除基本信息中已经显示的数据点
  const excludedTypes = [
    'description', 'disabled', 'debug', 'tag',
    'clientServer', 'protocol', 'port', 'uri', 'id', 'baud', 'pollPeriod',
    'variableType', 'value', 'units',
    'groupType',
    'actionType', 'triggerCondition'
  ]
  editablePoints.value = (node.points || []).filter(p => !excludedTypes.includes(p.type))

  // 初始化标签（使用text字段，这是标签的实际内容）
  // 只获取未被标记为删除的标签（tombstone !== 1）
  const tags = node.points?.filter(p => p.type === 'tag' && p.tombstone !== 1).map(p => p.text || '') || []
  formData.value.tags = tags
  
  // 根据类型初始化特定字段
  switch (node.type) {
    case 'modbus':
      formData.value.clientServer = getPointValue(node, 'clientServer', '')
      formData.value.modbusProtocol = getPointValue(node, 'protocol', '')
      formData.value.portUri = getPointValue(node, 'port', '') || getPointValue(node, 'uri', '')
      formData.value.deviceId = getPointValue(node, 'id', 1)
      formData.value.baud = getPointValue(node, 'baud', '')
      formData.value.pollPeriod = getPointValue(node, 'pollPeriod', 1000)
      break
      
    case 'variable':
      formData.value.variableType = getPointValue(node, 'variableType', '')
      formData.value.currentValue = getPointValue(node, 'value', '')
      formData.value.units = getPointValue(node, 'units', '')
      break
      
    case 'group':
      formData.value.groupType = getPointValue(node, 'groupType', '')
      break
      
    case 'action':
      formData.value.actionType = getPointValue(node, 'actionType', '')
      formData.value.triggerCondition = getPointValue(node, 'triggerCondition', '')
      break
  }
}

// 获取点的值
const getPointValue = (node: Node, type: string, defaultValue: any) => {
  const point = node.points.find(p => p.type === type)
  return point ? (point.text || point.value) : defaultValue
}

// 构建更新的数据点
const buildUpdatePoints = () => {
  const points = []
  const currentTime = new Date().toISOString()

  // 添加描述点
  if (formData.value.description) {
    points.push({
      type: 'description',
      key: '0',
      text: formData.value.description,
      time: currentTime
    })
  }

  // 使用编辑后的数据点（现在editablePoints已经排除了基本信息的数据点）
  editablePoints.value.forEach((point: any) => {
    points.push({
      type: point.type,
      key: point.key,
      value: point.value,
      text: point.text,
      time: currentTime
    })
  })

  // 处理标签：先删除所有现有标签，再添加新标签
  if (props.node) {
    // 1. 删除所有现有标签
    const existingTags = props.node.points.filter(p => p.type === 'tag' && p.tombstone !== 1)
    existingTags.forEach(tag => {
      points.push({
        type: 'tag',
        key: tag.key,
        text: tag.text || '',
        time: currentTime,
        tombstone: 1  // 标记为删除
      })
    })
  }

  // 2. 添加新的标签
  formData.value.tags.forEach((tag) => {
    if (tag && tag.trim()) {  // 确保标签不为空
      points.push({
        type: 'tag',
        key: tag.trim(),  // 使用标签内容作为key，确保唯一性
        text: tag.trim(),
        time: currentTime,
        tombstone: 0  // 标记为活跃
      })
    }
  })

  // 禁用状态点
  points.push({
    type: 'disabled',
    key: '0',
    value: formData.value.disabled ? 1 : 0,
    time: currentTime
  })

  // 调试级别
  points.push({
    type: 'debug',
    key: '0',
    value: formData.value.debugLevel,
    time: currentTime
  })
  
  // 根据类型添加特定点
  if (props.node) {
    switch (props.node.type) {
      case 'modbus':
        if (formData.value.clientServer) {
          points.push({
            type: 'clientServer',
            key: '0',
            time: currentTime,
            text: formData.value.clientServer
          })
        }
        if (formData.value.modbusProtocol) {
          points.push({
            type: 'protocol',
            key: '0',
            time: currentTime,
            text: formData.value.modbusProtocol
          })
        }
        if (formData.value.portUri) {
          points.push({
            type: formData.value.modbusProtocol === 'TCP' ? 'uri' : 'port',
            key: '0',
            time: currentTime,
            text: formData.value.portUri
          })
        }
        if (formData.value.deviceId) {
          points.push({
            type: 'id',
            key: '0',
            value: formData.value.deviceId,
            time: currentTime
          })
        }
        if (formData.value.baud) {
          points.push({
            type: 'baud',
            key: '0',
            time: currentTime,
            text: formData.value.baud
          })
        }
        if (formData.value.pollPeriod) {
          points.push({
            type: 'pollPeriod',
            key: '0',
            value: formData.value.pollPeriod,
            time: currentTime
          })
        }
        break
        
      case 'variable':
        if (formData.value.variableType) {
          points.push({
            type: 'variableType',
            key: '0',
            time: currentTime,
            text: formData.value.variableType
          })
        }
        if (formData.value.currentValue !== '') {
          points.push({
            type: 'value',
            key: '0',
            time: currentTime,
            text: formData.value.currentValue
          })
        }
        if (formData.value.units) {
          points.push({
            type: 'units',
            key: '0',
            time: currentTime,
            text: formData.value.units
          })
        }
        break
    }
  }

  return points
}

// 处理提交
const handleSubmit = async () => {
  if (!props.node) return

  try {
    loading.value = true

    const updateData: UpdateNodeSendRequest = {
      parent: formData.value.parent,
      oldParent: props.node.parent,  // 添加原父节点ID
      points: buildUpdatePoints()
    }

    await nodeStore.updateNode(props.node.id, updateData)

    message.success('设备更新成功')
    emit('success')
    handleCancel()
  } catch (error) {
    console.error('更新设备失败:', error)
    message.error('更新设备失败')
  } finally {
    loading.value = false
  }
}

// 处理确定
const handleOk = async () => {
  try {
    await formRef.value?.validate()
    await handleSubmit()
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

// 处理取消
const handleCancel = () => {
  visible.value = false
}

// 数据点管理相关函数
const markPointsChanged = () => {
  // 标记数据点已修改
}

const showAddPointModal = () => {
  newPoint.value = { type: '', key: '', value: undefined, text: '' }
  addPointModalVisible.value = true
}

const handleAddPoint = () => {
  if (!newPoint.value.type || !newPoint.value.key) {
    message.error('请填写数据点类型和键值')
    return
  }

  const point: Point = {
    type: newPoint.value.type,
    key: newPoint.value.key,
    value: newPoint.value.value,
    text: newPoint.value.text,
    time: new Date(),
    tombstone: 0,
    origin: ''
  }

  editablePoints.value.push(point)
  addPointModalVisible.value = false
  message.success('数据点添加成功')
}

const cancelAddPoint = () => {
  addPointModalVisible.value = false
}

const removePoint = (index: number) => {
  editablePoints.value.splice(index, 1)
  message.success('数据点删除成功')
}



// a-select的tags模式会自动处理标签的添加和删除

// 监听节点变化，初始化表单数据
watch(() => props.node, (newNode) => {
  if (newNode && visible.value) {
    initFormData()
  }
}, { immediate: true })

// 监听打开状态
watch(visible, (newVisible) => {
  if (newVisible) {
    if (props.node) {
      initFormData()
    }
    if (!nodeStore.nodes.data || nodeStore.nodes.data.length === 0) {
      nodeStore.fetchNodes()
    }
  }
})
</script>

<style scoped lang="less">
.type-specific-config {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>

<template>
  <div class="device-tree-view">
    <a-row :gutter="16">
      <!-- 左侧设备树 -->
      <a-col :span="10">
        <a-card title="设备树" size="small" class="tree-card">
          <template #extra>
            <a-space>
              <a-button type="text" size="small" @click="handleExpandAll">
                <template #icon>
                  <PlusSquareOutlined />
                </template>
                展开全部
              </a-button>
              <a-button type="text" size="small" @click="handleCollapseAll">
                <template #icon>
                  <MinusSquareOutlined />
                </template>
                收起全部
              </a-button>
            </a-space>
          </template>

          <div class="tree-search">
            <a-input-search
              v-model:value="searchValue"
              placeholder="搜索设备"
              @search="handleSearch"
              allow-clear
              size="small"
            />
          </div>

          <a-spin :spinning="loading">
            <a-tree
              v-model:expandedKeys="expandedKeys"
              v-model:selectedKeys="selectedKeys"
              :tree-data="filteredTreeData"
              :field-names="{ children: 'children', title: 'title', key: 'key' }"
              show-icon
              @select="handleNodeSelect"
              @right-click="handleRightClick"
              class="device-tree"
            >
              <template #icon="{ dataRef }">
                <component :is="getNodeIcon(dataRef.type)" />
              </template>
              <template #title="{ dataRef }">
                <div class="tree-node-title">
                  <span>{{ dataRef.title }}</span>
                  <a-tag v-if="dataRef.status" :color="getStatusColor(dataRef.status)" size="small">
                    {{ getStatusText(dataRef.status) }}
                  </a-tag>
                </div>
              </template>
            </a-tree>
          </a-spin>
        </a-card>
      </a-col>

      <!-- 右侧设备详情 -->
      <a-col :span="14">
        <a-card v-if="selectedNode" :title="selectedNode.title" size="small" class="detail-card">
          <template #extra>
            <a-space>
              <a-button type="primary" size="small" @click="$emit('edit', selectedNode.raw)">
                <template #icon>
                  <EditOutlined />
                </template>
                编辑
              </a-button>
              <a-button size="small" @click="$emit('config', selectedNode.raw)">
                <template #icon>
                  <SettingOutlined />
                </template>
                配置
              </a-button>
              <a-dropdown>
                <a-button size="small">
                  更多
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu @click="handleMenuClick">
                    <a-menu-item key="copy">
                      <CopyOutlined />
                      复制
                    </a-menu-item>
                    <a-menu-item key="move">
                      <DragOutlined />
                      移动
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" danger>
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>

          <!-- 设备基本信息 -->
          <div class="device-info">
            <a-descriptions :column="2" size="small">
              <a-descriptions-item label="设备ID">
                {{ selectedNode.raw.id }}
              </a-descriptions-item>
              <a-descriptions-item label="设备类型">
                <a-tag color="blue">{{ getTypeText(selectedNode.raw.type) }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="父节点">
                {{ selectedNode.raw.parent || '根节点' }}
              </a-descriptions-item>
              <a-descriptions-item label="状态">
                <a-tag :color="getStatusColor(selectedNode.status)">
                  {{ getStatusText(selectedNode.status) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="数据点数量">
                {{ selectedNode.raw.points.length }}
              </a-descriptions-item>
              <a-descriptions-item label="边缘点数量">
                {{ selectedNode.raw.edgePoints.length }}
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <!-- 数据点列表 -->
          <div v-if="filteredPoints.length > 0" class="points-section">
            <a-divider>数据点 ({{ filteredPoints.length }})</a-divider>
            <a-table
              :columns="pointColumns"
              :data-source="filteredPoints"
              :pagination="false"
              size="small"
              row-key="type"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'value'">
                  <span v-if="typeof record.value === 'boolean'">
                    <a-tag :color="record.value ? 'green' : 'red'">
                      {{ record.value ? '是' : '否' }}
                    </a-tag>
                  </span>
                  <span v-else>{{ record.text || record.value }}</span>
                </template>
                <template v-else-if="column.key === 'time'">
                  {{ formatTime(record.time) }}
                </template>
              </template>
            </a-table>
          </div>

          <!-- 子节点列表 -->
          <div v-if="selectedNode.children && selectedNode.children.length > 0" class="children-section">
            <a-divider>子节点 ({{ selectedNode.children.length }})</a-divider>
            <a-list
              :data-source="selectedNode.children"
              size="small"
              :pagination="false"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <component :is="getNodeIcon(item.type)" />
                    </template>
                    <template #title>
                      <a @click="handleNodeSelect([item.key])">{{ item.title }}</a>
                    </template>
                    <template #description>
                      {{ item.description }}
                    </template>
                  </a-list-item-meta>
                  <template #actions>
                    <a-tag :color="getStatusColor(item.status)" size="small">
                      {{ getStatusText(item.status) }}
                    </a-tag>
                  </template>
                </a-list-item>
              </template>
            </a-list>
          </div>
        </a-card>

        <!-- 未选择设备时的占位符 -->
        <a-card v-else title="设备详情" size="small" class="detail-card">
          <a-empty description="请从左侧选择一个设备查看详情" />
        </a-card>
      </a-col>
    </a-row>

    <!-- 右键菜单 -->
    <a-dropdown
      v-model:open="contextMenuVisible"
      :trigger="['contextmenu']"
      class="context-menu"
    >
      <div></div>
      <template #overlay>
        <a-menu @click="handleContextMenuClick">
          <a-menu-item key="edit">
            <EditOutlined />
            编辑
          </a-menu-item>
          <a-menu-item key="config">
            <SettingOutlined />
            配置
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="copy">
            <CopyOutlined />
            复制
          </a-menu-item>
          <a-menu-item key="move">
            <DragOutlined />
            移动
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="delete" danger>
            <DeleteOutlined />
            删除
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { format } from 'date-fns'
import {
  PlusSquareOutlined,
  MinusSquareOutlined,
  EditOutlined,
  SettingOutlined,
  DownOutlined,
  CopyOutlined,
  DragOutlined,
  DeleteOutlined,
  DatabaseOutlined,
  ApiOutlined,
  FunctionOutlined,
  GroupOutlined,
  PlayCircleOutlined
} from '@ant-design/icons-vue'
import type { NodeView, Node } from '@/types/node'
import { NodeType } from '@/types/common'

interface TreeNodeData {
  key: string
  title: string
  type: string
  status: string
  description: string
  children?: TreeNodeData[]
  raw: Node
}

interface Props {
  treeData: NodeView[]
  loading?: boolean
}

interface Emits {
  (e: 'select', node: Node): void
  (e: 'edit', node: Node): void
  (e: 'delete', node: Node): void
  (e: 'config', node: Node): void
  (e: 'move', nodeId: string, newParentId: string): void
  (e: 'copy', nodeId: string, targetParentId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 状态
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const searchValue = ref('')
const contextMenuVisible = ref(false)
const rightClickedNode = ref<TreeNodeData | null>(null)

// 数据点表格列
const pointColumns = [
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: '键', dataIndex: 'key', key: 'key', width: 100 },
  { title: '值', dataIndex: 'value', key: 'value', width: 150 },
  { title: '时间', dataIndex: 'time', key: 'time', width: 160 }
]

// 转换树形数据
const convertToTreeData = (nodes: NodeView[]): TreeNodeData[] => {
  return nodes.map(node => ({
    key: node.id,
    title: getNodeDescription(node),
    type: node.type,
    status: getNodeStatus(node),
    description: getNodeDescription(node),
    children: node.children ? convertToTreeData(node.children) : undefined,
    raw: node
  }))
}

// 过滤后的树形数据
const filteredTreeData = computed(() => {
  const treeData = convertToTreeData(props.treeData)
  
  if (!searchValue.value) {
    return treeData
  }
  
  // 递归过滤树形数据
  const filterTree = (nodes: TreeNodeData[]): TreeNodeData[] => {
    return nodes.filter(node => {
      const matchesSearch = node.title.toLowerCase().includes(searchValue.value.toLowerCase()) ||
                           node.key.toLowerCase().includes(searchValue.value.toLowerCase())
      
      if (node.children) {
        const filteredChildren = filterTree(node.children)
        if (filteredChildren.length > 0) {
          return true
        }
      }
      
      return matchesSearch
    }).map(node => ({
      ...node,
      children: node.children ? filterTree(node.children) : undefined
    }))
  }
  
  return filterTree(treeData)
})

// 选中的节点
const selectedNode = computed(() => {
  if (selectedKeys.value.length === 0) return null
  
  const findNode = (nodes: TreeNodeData[], key: string): TreeNodeData | null => {
    for (const node of nodes) {
      if (node.key === key) return node
      if (node.children) {
        const found = findNode(node.children, key)
        if (found) return found
      }
    }
    return null
  }
  
  return findNode(filteredTreeData.value, selectedKeys.value[0])
})

// 过滤后的数据点 - 排除tombstone=1的数据点
const filteredPoints = computed(() => {
  if (!selectedNode.value || !selectedNode.value.raw || !selectedNode.value.raw.points) return []
  return selectedNode.value.raw.points.filter(point => point.tombstone !== 1)
})

// 获取节点图标
const getNodeIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    [NodeType.Device]: DatabaseOutlined,
    [NodeType.Modbus]: ApiOutlined,
    [NodeType.Variable]: FunctionOutlined,
    [NodeType.Group]: GroupOutlined,
    [NodeType.Action]: PlayCircleOutlined
  }
  return iconMap[type] || SettingOutlined
}

// 获取节点描述
const getNodeDescription = (node: Node): string => {
  const descPoint = node.points.find(p => p.type === 'description')
  return descPoint?.text || (descPoint?.value ? String(descPoint.value) : '') || node.id
}

// 获取节点状态
const getNodeStatus = (node: Node): string => {
  const statePoint = node.points.find(p => p.type === 'sysState')
  if (statePoint) {
    switch (statePoint.text || statePoint.value) {
      case 'online':
        return 'online'
      case 'offline':
        return 'offline'
      case 'powerOff':
        return 'offline'
      default:
        return 'error'
    }
  }
  return 'offline'
}

// 获取状态颜色
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    online: 'green',
    offline: 'red',
    error: 'orange'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const textMap: Record<string, string> = {
    online: '在线',
    offline: '离线',
    error: '故障'
  }
  return textMap[status] || '未知'
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    [NodeType.Device]: '设备',
    [NodeType.Modbus]: 'Modbus',
    [NodeType.Variable]: '变量',
    [NodeType.Group]: '分组',
    [NodeType.Action]: '动作'
  }
  return textMap[type] || type
}

// 格式化时间
const formatTime = (time: Date | string | undefined | null): string => {
  if (!time) {
    return '-'
  }

  try {
    const date = time instanceof Date ? time : new Date(time)
    if (isNaN(date.getTime())) {
      return '-'
    }
    return format(date, 'yyyy-MM-dd HH:mm:ss')
  } catch (error) {
    console.warn('Invalid date format:', time, error)
    return '-'
  }
}

// 展开全部
const handleExpandAll = () => {
  const getAllKeys = (nodes: TreeNodeData[]): string[] => {
    let keys: string[] = []
    nodes.forEach(node => {
      keys.push(node.key)
      if (node.children) {
        keys = keys.concat(getAllKeys(node.children))
      }
    })
    return keys
  }
  
  expandedKeys.value = getAllKeys(filteredTreeData.value)
}

// 收起全部
const handleCollapseAll = () => {
  expandedKeys.value = []
}

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在computed中实现
}

// 节点选择处理
const handleNodeSelect = (keys: string[]) => {
  selectedKeys.value = keys
  if (keys.length > 0 && selectedNode.value) {
    emit('select', selectedNode.value.raw)
  }
}

// 右键点击处理
const handleRightClick = ({ node }: { node: any }) => {
  rightClickedNode.value = node.dataRef
  contextMenuVisible.value = true
}

// 菜单点击处理
const handleMenuClick = ({ key }: { key: string }) => {
  if (!selectedNode.value) return
  
  switch (key) {
    case 'copy':
      // 这里需要实现复制逻辑，暂时使用父节点作为目标
      emit('copy', selectedNode.value.key, selectedNode.value.raw.parent)
      break
    case 'move':
      // 这里需要实现移动逻辑
      break
    case 'delete':
      emit('delete', selectedNode.value.raw)
      break
  }
}

// 右键菜单点击处理
const handleContextMenuClick = ({ key }: { key: string }) => {
  if (!rightClickedNode.value) return
  
  switch (key) {
    case 'edit':
      emit('edit', rightClickedNode.value.raw)
      break
    case 'config':
      emit('config', rightClickedNode.value.raw)
      break
    case 'copy':
      emit('copy', rightClickedNode.value.key, rightClickedNode.value.raw.parent)
      break
    case 'delete':
      emit('delete', rightClickedNode.value.raw)
      break
  }
  
  contextMenuVisible.value = false
  rightClickedNode.value = null
}

// 监听树形数据变化，自动展开第一级
watch(() => props.treeData, (newData) => {
  if (newData.length > 0 && expandedKeys.value.length === 0) {
    expandedKeys.value = newData.map(node => node.id)
  }
}, { immediate: true })
</script>

<style scoped lang="less">
.device-tree-view {
  .tree-card, .detail-card {
    height: 600px;
    
    .ant-card-body {
      height: calc(100% - 57px);
      overflow: auto;
    }
  }
  
  .tree-search {
    margin-bottom: 16px;
  }
  
  .device-tree {
    .tree-node-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      
      span {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  
  .device-info {
    margin-bottom: 16px;
  }
  
  .points-section, .children-section {
    margin-top: 16px;
  }
  
  .context-menu {
    position: fixed;
    z-index: 1000;
  }
}
</style>

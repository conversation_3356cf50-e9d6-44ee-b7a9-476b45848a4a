<template>
  <div class="device-list-view">
    <a-table
      :columns="columns"
      :data-source="nodes"
      :loading="loading"
      :pagination="pagination"
      row-key="id"
      @change="handleTableChange"
      class="device-table"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <div class="device-name">
            <component :is="getNodeIcon(record.type)" class="device-icon" />
            <span>{{ getNodeDescription(record) }}</span>
          </div>
        </template>
        <template v-else-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record)">
            {{ getStatusText(record) }}
          </a-tag>
        </template>
        <template v-else-if="column.key === 'type'">
          <a-tag color="blue">{{ getTypeText(record.type) }}</a-tag>
        </template>
        <template v-else-if="column.key === 'lastSeen'">
          {{ formatTime(getLastUpdateTime(record)) }}
        </template>
        <template v-else-if="column.key === 'actions'">
          <a-space>
            <a-button type="link" size="small" @click="$emit('select', record)">
              查看
            </a-button>
            <a-button type="link" size="small" @click="$emit('edit', record)">
              编辑
            </a-button>
            <a-button type="link" size="small" @click="$emit('config', record)">
              配置
            </a-button>
            <a-popconfirm
              title="确定要删除这个设备吗？"
              @confirm="$emit('delete', record)"
            >
              <a-button type="link" size="small" danger>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { format } from 'date-fns'
import { 
  DatabaseOutlined,
  SettingOutlined,
  GroupOutlined,
  FunctionOutlined,
  PlayCircleOutlined,
  ApiOutlined
} from '@ant-design/icons-vue'
import type { Node } from '@/types/node'
import { NodeType } from '@/types/common'

interface Props {
  nodes: Node[]
  loading?: boolean
}

interface Emits {
  (e: 'select', node: Node): void
  (e: 'edit', node: Node): void
  (e: 'delete', node: Node): void
  (e: 'config', node: Node): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 表格列定义
const columns = [
  {
    title: '设备名称',
    dataIndex: 'name',
    key: 'name',
    sorter: true,
    width: 250
  },
  {
    title: '设备类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '父节点',
    dataIndex: 'parent',
    key: 'parent',
    width: 120
  },
  {
    title: '最后更新',
    dataIndex: 'lastSeen',
    key: 'lastSeen',
    width: 160,
    sorter: true
  },
  {
    title: '数据点数量',
    dataIndex: 'pointCount',
    key: 'pointCount',
    width: 120,
    customRender: ({ record }: { record: Node }) => record.points.length
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 获取节点图标
const getNodeIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    [NodeType.Device]: DatabaseOutlined,
    [NodeType.Modbus]: ApiOutlined,
    [NodeType.Variable]: FunctionOutlined,
    [NodeType.Group]: GroupOutlined,
    [NodeType.Action]: PlayCircleOutlined
  }
  return iconMap[type] || SettingOutlined
}

// 获取节点描述
const getNodeDescription = (node: Node): string => {
  const descPoint = node.points.find(p => p.type === 'description')
  return descPoint?.text || descPoint?.value || node.id
}

// 获取状态颜色
const getStatusColor = (node: Node): string => {
  const status = getNodeStatus(node)
  const colorMap: Record<string, string> = {
    online: 'green',
    offline: 'red',
    error: 'orange'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (node: Node): string => {
  const status = getNodeStatus(node)
  const textMap: Record<string, string> = {
    online: '在线',
    offline: '离线',
    error: '故障'
  }
  return textMap[status] || '未知'
}

// 获取节点状态
const getNodeStatus = (node: Node): string => {
  const statePoint = node.points.find(p => p.type === 'sysState')
  if (statePoint) {
    switch (statePoint.text || statePoint.value) {
      case 'online':
        return 'online'
      case 'offline':
        return 'offline'
      case 'powerOff':
        return 'offline'
      default:
        return 'error'
    }
  }
  return 'offline'
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    [NodeType.Device]: '设备',
    [NodeType.Modbus]: 'Modbus',
    [NodeType.Variable]: '变量',
    [NodeType.Group]: '分组',
    [NodeType.Action]: '动作',
    [NodeType.ModbusIO]: 'Modbus IO',
    [NodeType.OneWire]: 'OneWire',
    [NodeType.Serial]: '串口',
    [NodeType.Condition]: '条件',
    [NodeType.Rule]: '规则'
  }
  return textMap[type] || type
}

// 获取最后更新时间
const getLastUpdateTime = (node: Node): Date => {
  if (node.points.length === 0) {
    return new Date()
  }
  
  const latestPoint = node.points.reduce((latest, point) => {
    return point.time > latest.time ? point : latest
  })
  
  return latestPoint.time
}

// 格式化时间
const formatTime = (time: Date | string | undefined | null): string => {
  if (!time) {
    return '-'
  }

  try {
    const date = time instanceof Date ? time : new Date(time)
    if (isNaN(date.getTime())) {
      return '-'
    }
    return format(date, 'yyyy-MM-dd HH:mm:ss')
  } catch (error) {
    console.warn('Invalid date format:', time, error)
    return '-'
  }
}

// 表格变化处理
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
}

// 更新分页总数
const updatePagination = () => {
  pagination.value.total = props.nodes.length
}

// 监听节点变化
computed(() => {
  updatePagination()
  return props.nodes
})
</script>

<style scoped lang="less">
.device-list-view {
  .device-table {
    .device-name {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .device-icon {
        color: #1890ff;
      }
    }
    
    .ant-table-tbody > tr > td {
      padding: 12px 16px;
    }
  }
}
</style>

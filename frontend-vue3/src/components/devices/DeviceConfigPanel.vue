<template>
  <div class="device-config-panel">
    <!-- 设备基本信息编辑 -->
    <a-card title="设备配置" size="small" class="config-card">
      <template #extra>
        <a-space>
          <a-button type="primary" size="small" @click="handleSave" :loading="saving">
            <template #icon>
              <SaveOutlined />
            </template>
            保存
          </a-button>
          <a-button size="small" @click="handleDiscard">
            <template #icon>
              <UndoOutlined />
            </template>
            撤销
          </a-button>
        </a-space>
      </template>

      <!-- 设备描述编辑 -->
      <a-form layout="vertical" :model="formData">
        <a-form-item label="设备描述">
          <a-input 
            v-model:value="formData.description" 
            placeholder="请输入设备描述"
            @change="markAsChanged"
          />
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 数据点管理 -->
    <a-card title="数据点管理" size="small" class="points-card">
      <template #extra>
        <a-button type="dashed" size="small" @click="showAddPointModal">
          <template #icon>
            <PlusOutlined />
          </template>
          添加数据点
        </a-button>
      </template>

      <a-table
        :columns="pointColumns"
        :data-source="editablePoints"
        :pagination="false"
        size="small"
        row-key="key"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'type'">
            <a-input 
              v-model:value="record.type" 
              size="small"
              @change="markAsChanged"
            />
          </template>
          <template v-else-if="column.key === 'key'">
            <a-input 
              v-model:value="record.key" 
              size="small"
              @change="markAsChanged"
            />
          </template>
          <template v-else-if="column.key === 'value'">
            <a-input-number 
              v-model:value="record.value" 
              size="small"
              style="width: 100%"
              @change="markAsChanged"
            />
          </template>
          <template v-else-if="column.key === 'text'">
            <a-input 
              v-model:value="record.text" 
              size="small"
              @change="markAsChanged"
            />
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-button 
              type="text" 
              danger 
              size="small"
              @click="removePoint(index)"
            >
              <template #icon>
                <DeleteOutlined />
              </template>
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 标签管理 -->
    <a-card title="标签管理" size="small" class="tags-card">
      <template #extra>
        <a-button type="dashed" size="small" @click="showAddTagModal">
          <template #icon>
            <TagOutlined />
          </template>
          添加标签
        </a-button>
      </template>

      <div class="tags-container">
        <a-tag
          v-for="tag in deviceTags"
          :key="tag"
          closable
          @close="removeTag(tag)"
          color="blue"
        >
          {{ tag }}
        </a-tag>
      </div>
    </a-card>

    <!-- 添加数据点模态框 -->
    <a-modal
      v-model:open="addPointModalVisible"
      title="添加数据点"
      @ok="handleAddPoint"
      @cancel="cancelAddPoint"
    >
      <a-form layout="vertical" :model="newPoint">
        <a-form-item label="数据点类型" required>
          <a-input v-model:value="newPoint.type" placeholder="如: temperature, humidity" />
        </a-form-item>
        <a-form-item label="数据点键值" required>
          <a-input v-model:value="newPoint.key" placeholder="如: 0, 1, 2" />
        </a-form-item>
        <a-form-item label="数值">
          <a-input-number v-model:value="newPoint.value" style="width: 100%" />
        </a-form-item>
        <a-form-item label="文本描述">
          <a-input v-model:value="newPoint.text" placeholder="数据点描述" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 添加标签模态框 -->
    <a-modal
      v-model:open="addTagModalVisible"
      title="添加标签"
      @ok="handleAddTag"
      @cancel="cancelAddTag"
    >
      <a-form layout="vertical" :model="newTag">
        <a-form-item label="标签名称" required>
          <a-input v-model:value="newTag.name" placeholder="请输入标签名称" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  SaveOutlined,
  UndoOutlined,
  PlusOutlined,
  DeleteOutlined,
  TagOutlined
} from '@ant-design/icons-vue'
import type { Node, Point } from '@/types/node'

interface Props {
  node: Node | null
}

interface Emits {
  (e: 'save', data: any): void
  (e: 'change'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 状态
const saving = ref(false)
const hasChanges = ref(false)
const addPointModalVisible = ref(false)
const addTagModalVisible = ref(false)

// 表单数据
const formData = ref({
  description: ''
})

// 可编辑的数据点
const editablePoints = ref<Point[]>([])

// 设备标签
const deviceTags = ref<string[]>([])

// 新数据点
const newPoint = ref({
  type: '',
  key: '',
  value: undefined as number | undefined,
  text: ''
})

// 新标签
const newTag = ref({
  name: ''
})

// 数据点表格列
const pointColumns = [
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: '键值', dataIndex: 'key', key: 'key', width: 80 },
  { title: '数值', dataIndex: 'value', key: 'value', width: 100 },
  { title: '文本', dataIndex: 'text', key: 'text' },
  { title: '操作', key: 'actions', width: 80 }
]

// 获取节点描述
const getNodeDescription = (node: Node): string => {
  const descPoint = node.points?.find(p => p.type === 'description')
  return descPoint?.text || ''
}

// 提取标签
const extractTags = (node: Node): string[] => {
  return node.points?.filter(p => p.type === 'tag').map(p => p.text || '') || []
}

// 加载节点数据
const loadNodeData = (node: Node) => {
  formData.value.description = getNodeDescription(node)
  editablePoints.value = [...(node.points || [])]
  deviceTags.value = extractTags(node)
  hasChanges.value = false
}

// 监听节点变化
watch(() => props.node, (newNode) => {
  if (newNode) {
    loadNodeData(newNode)
  }
}, { immediate: true })

// 标记为已修改
const markAsChanged = () => {
  hasChanges.value = true
  emit('change')
}

// 保存配置
const handleSave = async () => {
  if (!props.node) return
  
  saving.value = true
  try {
    const updateData = {
      description: formData.value.description,
      points: editablePoints.value,
      tags: deviceTags.value
    }
    
    emit('save', updateData)
    hasChanges.value = false
    message.success('配置保存成功')
  } catch (error) {
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 撤销修改
const handleDiscard = () => {
  if (props.node) {
    loadNodeData(props.node)
    message.info('已撤销修改')
  }
}

// 显示添加数据点模态框
const showAddPointModal = () => {
  newPoint.value = { type: '', key: '', value: undefined, text: '' }
  addPointModalVisible.value = true
}

// 添加数据点
const handleAddPoint = () => {
  if (!newPoint.value.type || !newPoint.value.key) {
    message.error('请填写数据点类型和键值')
    return
  }

  const point: Point = {
    type: newPoint.value.type,
    key: newPoint.value.key,
    value: newPoint.value.value,
    text: newPoint.value.text,
    time: new Date(),
    tombstone: 0,
    origin: ''
  }

  editablePoints.value.push(point)
  markAsChanged()
  addPointModalVisible.value = false
  message.success('数据点添加成功')
}

// 取消添加数据点
const cancelAddPoint = () => {
  addPointModalVisible.value = false
}

// 删除数据点
const removePoint = (index: number) => {
  editablePoints.value.splice(index, 1)
  markAsChanged()
  message.success('数据点删除成功')
}

// 显示添加标签模态框
const showAddTagModal = () => {
  newTag.value = { name: '' }
  addTagModalVisible.value = true
}

// 添加标签
const handleAddTag = () => {
  if (!newTag.value.name) {
    message.error('请输入标签名称')
    return
  }
  
  if (deviceTags.value.includes(newTag.value.name)) {
    message.error('标签已存在')
    return
  }
  
  deviceTags.value.push(newTag.value.name)
  markAsChanged()
  addTagModalVisible.value = false
  message.success('标签添加成功')
}

// 取消添加标签
const cancelAddTag = () => {
  addTagModalVisible.value = false
}

// 删除标签
const removeTag = (tag: string) => {
  const index = deviceTags.value.indexOf(tag)
  if (index > -1) {
    deviceTags.value.splice(index, 1)
    markAsChanged()
    message.success('标签删除成功')
  }
}
</script>

<style scoped lang="less">
.device-config-panel {
  .config-card,
  .points-card,
  .tags-card {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .tags-container {
    min-height: 32px;
    
    .ant-tag {
      margin-bottom: 8px;
    }
  }
}
</style>

<template>
  <div class="device-grid-container">
    <!-- 分组展示模式 -->
    <div v-if="groupMode && groupedDevices.length > 0" class="grouped-devices">
      <div 
        v-for="group in groupedDevices" 
        :key="group.id"
        class="device-group"
      >
        <!-- 分组头部 -->
        <div class="group-header" @click="toggleGroup(group.id)">
          <div class="group-info">
            <DeviceIcons :type="group.type" size="small" />
            <span class="group-name">{{ group.name }}</span>
            <a-tag color="blue" size="small">{{ group.children.length }}</a-tag>
          </div>
          <div class="group-actions">
            <a-button 
              type="text" 
              size="small"
              @click.stop="handleGroupAction(group, 'add')"
            >
              <PlusOutlined />
            </a-button>
            <a-button 
              type="text" 
              size="small"
              @click.stop="toggleGroup(group.id)"
            >
              <DownOutlined 
                :class="{ 'rotate-180': !expandedGroups.includes(group.id) }" 
              />
            </a-button>
          </div>
        </div>

        <!-- 分组设备网格 -->
        <a-collapse-transition>
          <div 
            v-show="expandedGroups.includes(group.id)"
            class="group-devices"
          >
            <div class="device-grid">
              <DeviceCard
                v-for="device in group.children"
                :key="device.id"
                :device="device"
                :selected="selectedDevices.includes(device.id)"
                :children-count="getChildrenCount(device.id)"
                @click="handleDeviceClick"
                @edit="handleDeviceEdit"
                @configure="handleDeviceConfigure"
                @copy="handleDeviceCopy"
                @move="handleDeviceMove"
                @delete="handleDeviceDelete"
              />
            </div>
          </div>
        </a-collapse-transition>
      </div>
    </div>

    <!-- 平铺网格模式 -->
    <div v-else class="flat-devices">
      <div class="device-grid">
        <DeviceCard
          v-for="device in displayDevices"
          :key="device.id"
          :device="device"
          :selected="selectedDevices.includes(device.id)"
          :children-count="getChildrenCount(device.id)"
          @click="handleDeviceClick"
          @edit="handleDeviceEdit"
          @configure="handleDeviceConfigure"
          @copy="handleDeviceCopy"
          @move="handleDeviceMove"
          @delete="handleDeviceDelete"
        />
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="displayDevices.length === 0" class="empty-state">
      <a-empty 
        description="没有找到符合条件的设备"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      >
        <template #image>
          <DatabaseOutlined style="font-size: 64px; color: #d9d9d9;" />
        </template>
        <a-button type="primary" @click="handleAddDevice">
          <PlusOutlined />
          添加设备
        </a-button>
      </a-empty>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore && !loading" class="load-more">
      <a-button @click="handleLoadMore" block>
        加载更多设备
      </a-button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <a-spin size="large" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Empty } from 'ant-design-vue'
import {
  PlusOutlined,
  DownOutlined,
  DatabaseOutlined
} from '@ant-design/icons-vue'
import DeviceCard from './DeviceCard.vue'
import DeviceIcons from '@/components/icons/DeviceIcons.vue'
import type { Node } from '@/types/node'

// 分组设备接口
interface DeviceGroup {
  id: string
  name: string
  type: string
  children: Node[]
}

// 组件属性
interface Props {
  devices: Node[]
  groupMode?: boolean
  selectedDevices?: string[]
  loading?: boolean
  hasMore?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  groupMode: true,
  selectedDevices: () => [],
  loading: false,
  hasMore: false
})

// 事件定义
const emit = defineEmits<{
  deviceClick: [device: Node]
  deviceEdit: [device: Node]
  deviceConfigure: [device: Node]
  deviceCopy: [device: Node]
  deviceMove: [device: Node]
  deviceDelete: [device: Node]
  groupAction: [group: DeviceGroup, action: string]
  addDevice: []
  loadMore: []
}>()

// 响应式数据
const expandedGroups = ref<string[]>([])

// 计算显示的设备列表
const displayDevices = computed(() => {
  return props.devices.filter(device => device.tombstone !== 1)
})

// 计算分组设备
const groupedDevices = computed((): DeviceGroup[] => {
  if (!props.groupMode) return []

  const groups = new Map<string, DeviceGroup>()
  const rootDevices: Node[] = []

  // 分离根设备和子设备
  displayDevices.value.forEach(device => {
    if (!device.parent) {
      rootDevices.push(device)
    }
  })

  // 为每个根设备创建分组
  rootDevices.forEach(rootDevice => {
    const children = displayDevices.value.filter(device => 
      device.parent === rootDevice.id
    )

    groups.set(rootDevice.id, {
      id: rootDevice.id,
      name: getDeviceName(rootDevice),
      type: rootDevice.type,
      children: [rootDevice, ...children]
    })
  })

  // 处理没有父设备的孤立设备
  const orphanDevices = displayDevices.value.filter(device => 
    device.parent && !displayDevices.value.find(d => d.id === device.parent)
  )

  if (orphanDevices.length > 0) {
    groups.set('orphan', {
      id: 'orphan',
      name: '其他设备',
      type: 'group',
      children: orphanDevices
    })
  }

  return Array.from(groups.values())
})

// 获取设备名称
const getDeviceName = (device: Node): string => {
  const descPoint = device.points.find(p => p.type === 'description')
  return descPoint?.text || descPoint?.value?.toString() || device.id
}

// 获取子设备数量
const getChildrenCount = (deviceId: string): number => {
  return displayDevices.value.filter(device => device.parent === deviceId).length
}

// 切换分组展开/收起
const toggleGroup = (groupId: string) => {
  const index = expandedGroups.value.indexOf(groupId)
  if (index > -1) {
    expandedGroups.value.splice(index, 1)
  } else {
    expandedGroups.value.push(groupId)
  }
}

// 处理设备点击
const handleDeviceClick = (device: Node) => {
  emit('deviceClick', device)
}

// 处理设备编辑
const handleDeviceEdit = (device: Node) => {
  emit('deviceEdit', device)
}

// 处理设备配置
const handleDeviceConfigure = (device: Node) => {
  emit('deviceConfigure', device)
}

// 处理设备复制
const handleDeviceCopy = (device: Node) => {
  emit('deviceCopy', device)
}

// 处理设备移动
const handleDeviceMove = (device: Node) => {
  emit('deviceMove', device)
}

// 处理设备删除
const handleDeviceDelete = (device: Node) => {
  emit('deviceDelete', device)
}

// 处理分组操作
const handleGroupAction = (group: DeviceGroup, action: string) => {
  emit('groupAction', group, action)
}

// 处理添加设备
const handleAddDevice = () => {
  emit('addDevice')
}

// 处理加载更多
const handleLoadMore = () => {
  emit('loadMore')
}

// 监听设备变化，自动展开有设备的分组
watch(
  () => props.devices,
  () => {
    if (props.groupMode && groupedDevices.value.length > 0) {
      // 自动展开前3个分组
      expandedGroups.value = groupedDevices.value
        .slice(0, 3)
        .map(group => group.id)
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="less">
@import '@/styles/variables.less';

.device-grid-container {
  .device-group {
    margin-bottom: 24px;
    border: 1px solid @border-color-base;
    border-radius: @border-radius-base;
    overflow: hidden;

    .group-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background-color: #fafafa;
      border-bottom: 1px solid @border-color-base;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &:hover {
        background-color: #f0f0f0;
      }

      .group-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .group-name {
          font-weight: 500;
          font-size: 16px;
        }
      }

      .group-actions {
        display: flex;
        align-items: center;
        gap: 4px;

        .rotate-180 {
          transform: rotate(180deg);
          transition: transform 0.3s ease;
        }
      }
    }

    .group-devices {
      padding: 16px;
    }
  }

  .device-grid {
    display: grid;
    gap: @device-card-gap;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));

    // 响应式网格
    @media (max-width: 576px) {
      grid-template-columns: 1fr;
    }

    @media (min-width: 577px) and (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 769px) and (max-width: 1200px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (min-width: 1201px) and (max-width: 1600px) {
      grid-template-columns: repeat(4, 1fr);
    }

    @media (min-width: 1601px) {
      grid-template-columns: repeat(5, 1fr);
    }
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }

  .load-more {
    margin-top: 24px;
    text-align: center;
  }

  .loading-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }
}
</style>

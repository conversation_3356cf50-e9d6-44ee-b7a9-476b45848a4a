<template>
  <a-card 
    :class="['device-card', { 'device-card-selected': selected }]"
    :hoverable="true"
    @click="handleCardClick"
  >
    <!-- 卡片头部 -->
    <template #title>
      <div class="device-card-header">
        <div class="device-info">
          <DeviceIcons :type="device.type" size="default" />
          <span class="device-name">{{ deviceName }}</span>
        </div>
        <DeviceStatusIndicator 
          :status="deviceStatus" 
          :show-text="false"
          :show-icon="true"
        />
      </div>
    </template>

    <!-- 快速操作按钮 -->
    <template #extra>
      <a-dropdown :trigger="['click']" placement="bottomRight">
        <a-button type="text" size="small" @click.stop>
          <MoreOutlined />
        </a-button>
        <template #overlay>
          <a-menu @click="handleMenuClick">
            <a-menu-item key="edit">
              <EditOutlined />
              编辑设备
            </a-menu-item>
            <a-menu-item key="configure">
              <SettingOutlined />
              设备配置
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="copy">
              <CopyOutlined />
              复制设备
            </a-menu-item>
            <a-menu-item key="move">
              <DragOutlined />
              移动设备
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="delete" danger>
              <DeleteOutlined />
              删除设备
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>

    <!-- 卡片内容 -->
    <div class="device-card-content">
      <!-- 设备描述 -->
      <div class="device-description">
        {{ deviceDescription }}
      </div>

      <!-- 设备类型标签 -->
      <div class="device-type">
        <a-tag :color="getTypeColor(device.type)">
          {{ getTypeText(device.type) }}
        </a-tag>
      </div>

      <!-- 关键信息展示 -->
      <div v-if="keyMetrics.length > 0" class="device-metrics">
        <div 
          v-for="metric in keyMetrics" 
          :key="metric.key"
          class="metric-item"
        >
          <span class="metric-label">{{ metric.label }}:</span>
          <span class="metric-value">{{ metric.value }}</span>
        </div>
      </div>

      <!-- 子设备数量 -->
      <div v-if="childrenCount > 0" class="children-info">
        <GroupOutlined />
        <span>{{ childrenCount }} 个子设备</span>
      </div>

      <!-- 最后更新时间 -->
      <div class="update-time">
        <ClockCircleOutlined />
        <span>{{ formatTime(device.updateTime) }}</span>
      </div>
    </div>

    <!-- 状态指示条 -->
    <div :class="['status-bar', `status-bar-${deviceStatus}`]"></div>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { format } from 'date-fns'
import {
  MoreOutlined,
  EditOutlined,
  SettingOutlined,
  CopyOutlined,
  DragOutlined,
  DeleteOutlined,
  GroupOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import DeviceIcons from '@/components/icons/DeviceIcons.vue'
import DeviceStatusIndicator from './DeviceStatusIndicator.vue'
import type { Node } from '@/types/node'

// 组件属性
interface Props {
  device: Node
  selected?: boolean
  childrenCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  childrenCount: 0
})

// 事件定义
const emit = defineEmits<{
  click: [device: Node]
  edit: [device: Node]
  configure: [device: Node]
  copy: [device: Node]
  move: [device: Node]
  delete: [device: Node]
}>()

// 获取设备名称
const deviceName = computed(() => {
  const descPoint = props.device.points.find(p => p.type === 'description')
  return descPoint?.text || descPoint?.value?.toString() || props.device.id
})

// 获取设备描述
const deviceDescription = computed(() => {
  const name = deviceName.value
  return name.length > 20 ? `${name.substring(0, 20)}...` : name
})

// 获取设备状态
const deviceStatus = computed(() => {
  const statePoint = props.device.points.find(p => p.type === 'sysState')
  if (statePoint) {
    switch (statePoint.text || statePoint.value) {
      case 'online':
        return 'online'
      case 'offline':
      case 'powerOff':
        return 'offline'
      default:
        return 'error'
    }
  }
  return 'offline'
})

// 获取关键指标
const keyMetrics = computed(() => {
  const metrics: Array<{ key: string; label: string; value: string }> = []
  
  // 根据设备类型显示不同的关键指标
  props.device.points.forEach(point => {
    if (point.tombstone === 1) return
    
    switch (point.type) {
      case 'temperature':
        metrics.push({
          key: 'temperature',
          label: '温度',
          value: `${point.value}°C`
        })
        break
      case 'humidity':
        metrics.push({
          key: 'humidity',
          label: '湿度',
          value: `${point.value}%`
        })
        break
      case 'voltage':
        metrics.push({
          key: 'voltage',
          label: '电压',
          value: `${point.value}V`
        })
        break
      case 'current':
        metrics.push({
          key: 'current',
          label: '电流',
          value: `${point.value}A`
        })
        break
    }
  })
  
  return metrics.slice(0, 2) // 最多显示2个关键指标
})

// 获取设备类型文本
const getTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    device: '设备',
    group: '分组',
    modbus: 'Modbus设备',
    serial: '串口设备',
    canbus: 'CAN设备',
    user: '用户',
    temperature: '温度传感器',
    humidity: '湿度传感器',
    switch: '开关设备',
    gateway: '网关设备'
  }
  return textMap[type] || type
}

// 获取设备类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    device: 'blue',
    group: 'purple',
    modbus: 'cyan',
    serial: 'cyan',
    canbus: 'cyan',
    temperature: 'orange',
    humidity: 'blue',
    switch: 'purple',
    gateway: 'green'
  }
  return colorMap[type] || 'default'
}

// 格式化时间
const formatTime = (time: Date | string | undefined | null) => {
  if (!time) return '未知'
  
  try {
    const date = time instanceof Date ? time : new Date(time)
    if (isNaN(date.getTime())) return '未知'
    return format(date, 'MM-dd HH:mm')
  } catch {
    return '未知'
  }
}

// 处理卡片点击
const handleCardClick = () => {
  emit('click', props.device)
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'edit':
      emit('edit', props.device)
      break
    case 'configure':
      emit('configure', props.device)
      break
    case 'copy':
      emit('copy', props.device)
      break
    case 'move':
      emit('move', props.device)
      break
    case 'delete':
      emit('delete', props.device)
      break
  }
}
</script>

<style scoped lang="less">
@import '@/styles/variables.less';

.device-card {
  position: relative;
  border-radius: @device-card-border-radius;
  box-shadow: @device-card-shadow;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;

  &:hover {
    box-shadow: @device-card-hover-shadow;
    transform: translateY(-2px);
  }

  &.device-card-selected {
    border-color: @primary-color;
    box-shadow: 0 0 0 2px fade(@primary-color, 20%);
  }

  .device-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .device-info {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .device-name {
        font-weight: 500;
        font-size: 16px;
      }
    }
  }

  .device-card-content {
    .device-description {
      color: @text-color-secondary;
      margin-bottom: 12px;
      font-size: 13px;
    }

    .device-type {
      margin-bottom: 12px;
    }

    .device-metrics {
      margin-bottom: 12px;
      
      .metric-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        font-size: 12px;
        
        .metric-label {
          color: @text-color-secondary;
        }
        
        .metric-value {
          font-weight: 500;
          color: @primary-color;
        }
      }
    }

    .children-info {
      display: flex;
      align-items: center;
      gap: 4px;
      color: @text-color-secondary;
      font-size: 12px;
      margin-bottom: 8px;
    }

    .update-time {
      display: flex;
      align-items: center;
      gap: 4px;
      color: @text-color-secondary;
      font-size: 12px;
    }
  }

  .status-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    
    &.status-bar-online {
      background-color: @status-online;
    }
    
    &.status-bar-offline {
      background-color: @status-offline;
    }
    
    &.status-bar-error {
      background-color: @status-error;
    }
  }
}
</style>

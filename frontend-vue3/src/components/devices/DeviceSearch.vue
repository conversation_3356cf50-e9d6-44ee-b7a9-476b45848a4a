<template>
  <div class="device-search">
    <a-row :gutter="16" align="middle">
      <!-- 搜索输入框 -->
      <a-col :xs="24" :sm="12" :md="8">
        <a-input-search
          v-model:value="searchKeyword"
          placeholder="搜索设备名称或ID"
          allow-clear
          @search="handleSearch"
          @change="handleSearchChange"
        >
          <template #prefix>
            <SearchOutlined />
          </template>
        </a-input-search>
      </a-col>

      <!-- 设备类型筛选 -->
      <a-col :xs="24" :sm="6" :md="4">
        <a-select
          v-model:value="selectedType"
          placeholder="设备类型"
          allow-clear
          @change="handleTypeChange"
          style="width: 100%"
        >
          <a-select-option value="">全部类型</a-select-option>
          <a-select-option 
            v-for="type in deviceTypes" 
            :key="type.value" 
            :value="type.value"
          >
            {{ type.label }}
          </a-select-option>
        </a-select>
      </a-col>

      <!-- 设备状态筛选 -->
      <a-col :xs="24" :sm="6" :md="4">
        <a-select
          v-model:value="selectedStatus"
          placeholder="设备状态"
          allow-clear
          @change="handleStatusChange"
          style="width: 100%"
        >
          <a-select-option value="">全部状态</a-select-option>
          <a-select-option value="online">
            <a-badge status="success" text="在线" />
          </a-select-option>
          <a-select-option value="offline">
            <a-badge status="default" text="离线" />
          </a-select-option>
          <a-select-option value="error">
            <a-badge status="error" text="故障" />
          </a-select-option>
        </a-select>
      </a-col>

      <!-- 高级筛选按钮 -->
      <a-col :xs="24" :sm="12" :md="4">
        <a-space>
          <a-button @click="toggleAdvancedFilter">
            <FilterOutlined />
            高级筛选
          </a-button>
          <a-button @click="handleReset">
            <ClearOutlined />
            重置
          </a-button>
        </a-space>
      </a-col>

      <!-- 视图切换 -->
      <a-col :xs="24" :sm="12" :md="4">
        <a-radio-group 
          v-model:value="viewMode" 
          button-style="solid" 
          size="small"
          @change="handleViewModeChange"
        >
          <a-radio-button value="card">
            <AppstoreOutlined />
          </a-radio-button>
          <a-radio-button value="list">
            <UnorderedListOutlined />
          </a-radio-button>
        </a-radio-group>
      </a-col>
    </a-row>

    <!-- 高级筛选面板 -->
    <a-collapse v-if="showAdvancedFilter" class="advanced-filter">
      <a-collapse-panel key="advanced" header="高级筛选选项">
        <a-row :gutter="16">
          <!-- 父节点筛选 -->
          <a-col :span="8">
            <a-form-item label="父节点">
              <a-select
                v-model:value="selectedParent"
                placeholder="选择父节点"
                allow-clear
                show-search
                @change="handleParentChange"
              >
                <a-select-option value="">全部节点</a-select-option>
                <a-select-option 
                  v-for="parent in parentNodes" 
                  :key="parent.id" 
                  :value="parent.id"
                >
                  {{ parent.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 创建时间范围 -->
          <a-col :span="8">
            <a-form-item label="创建时间">
              <a-range-picker
                v-model:value="dateRange"
                @change="handleDateRangeChange"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <!-- 是否有子节点 -->
          <a-col :span="8">
            <a-form-item label="子节点">
              <a-select
                v-model:value="hasChildren"
                placeholder="是否有子节点"
                allow-clear
                @change="handleChildrenChange"
              >
                <a-select-option value="">不限</a-select-option>
                <a-select-option value="true">有子节点</a-select-option>
                <a-select-option value="false">无子节点</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-collapse-panel>
    </a-collapse>

    <!-- 搜索结果统计 -->
    <div v-if="showResultStats" class="search-stats">
      <a-space>
        <span>找到 {{ totalCount }} 个设备</span>
        <a-divider type="vertical" />
        <span>在线: {{ onlineCount }}</span>
        <span>离线: {{ offlineCount }}</span>
        <span>故障: {{ errorCount }}</span>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  AppstoreOutlined,
  UnorderedListOutlined
} from '@ant-design/icons-vue'
import type { Dayjs } from 'dayjs'

// 组件属性
interface Props {
  totalCount?: number
  onlineCount?: number
  offlineCount?: number
  errorCount?: number
  showResultStats?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  totalCount: 0,
  onlineCount: 0,
  offlineCount: 0,
  errorCount: 0,
  showResultStats: true
})

// 事件定义
const emit = defineEmits<{
  search: [filters: SearchFilters]
  viewModeChange: [mode: 'card' | 'list']
}>()

// 搜索筛选接口
interface SearchFilters {
  keyword: string
  type: string
  status: string
  parent: string
  dateRange: [Dayjs, Dayjs] | null
  hasChildren: string
  viewMode: 'card' | 'list'
}

// 响应式数据
const searchKeyword = ref('')
const selectedType = ref('')
const selectedStatus = ref('')
const selectedParent = ref('')
const dateRange = ref<[Dayjs, Dayjs] | null>(null)
const hasChildren = ref('')
const viewMode = ref<'card' | 'list'>('card')
const showAdvancedFilter = ref(false)

// 设备类型选项
const deviceTypes = [
  { value: 'device', label: '基础设备' },
  { value: 'group', label: '设备分组' },
  { value: 'modbus', label: 'Modbus设备' },
  { value: 'serial', label: '串口设备' },
  { value: 'canbus', label: 'CAN设备' },
  { value: 'temperature', label: '温度传感器' },
  { value: 'humidity', label: '湿度传感器' },
  { value: 'switch', label: '开关设备' },
  { value: 'gateway', label: '网关设备' }
]

// 父节点选项（这里应该从props或store获取）
const parentNodes = ref([
  { id: 'root', name: '根节点' },
  { id: 'group1', name: '传感器组' },
  { id: 'group2', name: '执行器组' }
])

// 计算当前筛选条件
const currentFilters = computed((): SearchFilters => ({
  keyword: searchKeyword.value,
  type: selectedType.value,
  status: selectedStatus.value,
  parent: selectedParent.value,
  dateRange: dateRange.value,
  hasChildren: hasChildren.value,
  viewMode: viewMode.value
}))

// 处理搜索
const handleSearch = () => {
  emit('search', currentFilters.value)
}

// 处理搜索输入变化
const handleSearchChange = () => {
  // 实时搜索，可以添加防抖
  handleSearch()
}

// 处理类型筛选变化
const handleTypeChange = () => {
  handleSearch()
}

// 处理状态筛选变化
const handleStatusChange = () => {
  handleSearch()
}

// 处理父节点筛选变化
const handleParentChange = () => {
  handleSearch()
}

// 处理日期范围变化
const handleDateRangeChange = () => {
  handleSearch()
}

// 处理子节点筛选变化
const handleChildrenChange = () => {
  handleSearch()
}

// 处理视图模式变化
const handleViewModeChange = () => {
  emit('viewModeChange', viewMode.value)
  handleSearch()
}

// 切换高级筛选
const toggleAdvancedFilter = () => {
  showAdvancedFilter.value = !showAdvancedFilter.value
}

// 重置筛选条件
const handleReset = () => {
  searchKeyword.value = ''
  selectedType.value = ''
  selectedStatus.value = ''
  selectedParent.value = ''
  dateRange.value = null
  hasChildren.value = ''
  showAdvancedFilter.value = false
  handleSearch()
}

// 监听筛选条件变化
watch(currentFilters, () => {
  // 可以在这里添加防抖逻辑
}, { deep: true })
</script>

<style scoped lang="less">
.device-search {
  margin-bottom: 16px;

  .advanced-filter {
    margin-top: 16px;
  }

  .search-stats {
    margin-top: 12px;
    padding: 8px 12px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-size: 13px;
    color: #666;
  }

  // 响应式布局调整
  @media (max-width: 768px) {
    .ant-col {
      margin-bottom: 8px;
    }
  }
}
</style>

<template>
  <div class="device-status-indicator">
    <a-badge 
      :status="badgeStatus" 
      :text="statusText"
      :class="['status-badge', `status-${status}`]"
    />
    <div v-if="showIcon" class="status-icon">
      <component :is="statusIcon" :class="['icon', `icon-${status}`]" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  CloseCircleOutlined,
  MinusCircleOutlined 
} from '@ant-design/icons-vue'

// 组件属性
interface Props {
  status: 'online' | 'offline' | 'error' | 'unknown'
  showText?: boolean
  showIcon?: boolean
  size?: 'small' | 'default' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  showText: true,
  showIcon: false,
  size: 'default'
})

// 状态映射
const statusConfig = {
  online: {
    badge: 'success' as const,
    text: '运行中',
    icon: CheckCircleOutlined,
    color: '#52c41a'
  },
  offline: {
    badge: 'default' as const,
    text: '离线',
    icon: MinusCircleOutlined,
    color: '#d9d9d9'
  },
  error: {
    badge: 'error' as const,
    text: '故障',
    icon: CloseCircleOutlined,
    color: '#ff4d4f'
  },
  unknown: {
    badge: 'warning' as const,
    text: '未知',
    icon: ExclamationCircleOutlined,
    color: '#faad14'
  }
}

// 计算属性
const badgeStatus = computed(() => statusConfig[props.status].badge)
const statusText = computed(() => props.showText ? statusConfig[props.status].text : '')
const statusIcon = computed(() => statusConfig[props.status].icon)
const statusColor = computed(() => statusConfig[props.status].color)
</script>

<style scoped lang="less">
.device-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;

  .status-badge {
    &.status-online {
      :deep(.ant-badge-status-dot) {
        background-color: #52c41a;
        animation: pulse 2s infinite;
      }
    }

    &.status-offline {
      :deep(.ant-badge-status-dot) {
        background-color: #d9d9d9;
      }
    }

    &.status-error {
      :deep(.ant-badge-status-dot) {
        background-color: #ff4d4f;
        animation: pulse 2s infinite;
      }
    }

    &.status-unknown {
      :deep(.ant-badge-status-dot) {
        background-color: #faad14;
      }
    }
  }

  .status-icon {
    .icon {
      font-size: 16px;

      &.icon-online {
        color: #52c41a;
      }

      &.icon-offline {
        color: #d9d9d9;
      }

      &.icon-error {
        color: #ff4d4f;
      }

      &.icon-unknown {
        color: #faad14;
      }
    }
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 响应式尺寸
.device-status-indicator {
  &.size-small {
    .status-icon .icon {
      font-size: 12px;
    }
  }

  &.size-large {
    .status-icon .icon {
      font-size: 20px;
    }
  }
}
</style>

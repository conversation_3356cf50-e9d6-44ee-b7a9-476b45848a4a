<template>
  <a-modal
    v-model:open="visible"
    title="添加设备"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirm-loading="loading"
    width="600px"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      @finish="handleSubmit"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="设备类型" name="type">
            <a-select 
              v-model:value="formData.type" 
              placeholder="请选择设备类型"
              @change="handleTypeChange"
            >
              <a-select-option value="device">
                <DatabaseOutlined /> 设备
              </a-select-option>
              <a-select-option value="modbus">
                <ApiOutlined /> Modbus
              </a-select-option>
              <a-select-option value="variable">
                <FunctionOutlined /> 变量
              </a-select-option>
              <a-select-option value="group">
                <GroupOutlined /> 分组
              </a-select-option>
              <a-select-option value="action">
                <PlayCircleOutlined /> 动作
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="父节点" name="parent">
            <a-tree-select
              v-model:value="formData.parent"
              :tree-data="parentOptions"
              placeholder="请选择父节点"
              tree-default-expand-all
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="设备描述" name="description">
        <a-input 
          v-model:value="formData.description" 
          placeholder="请输入设备描述"
        />
      </a-form-item>

      <!-- 根据设备类型显示不同的配置项 -->
      <div v-if="formData.type" class="type-specific-config">
        <a-divider>{{ getTypeText(formData.type) }}配置</a-divider>
        
        <!-- 设备类型配置 -->
        <template v-if="formData.type === 'device'">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="设备地址" name="deviceAddress">
                <a-input 
                  v-model:value="formData.deviceAddress" 
                  placeholder="设备地址"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="通信协议" name="protocol">
                <a-select v-model:value="formData.protocol" placeholder="选择协议">
                  <a-select-option value="tcp">TCP</a-select-option>
                  <a-select-option value="serial">串口</a-select-option>
                  <a-select-option value="modbus">Modbus</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </template>

        <!-- Modbus类型配置 -->
        <template v-if="formData.type === 'modbus'">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="客户端/服务端" name="clientServer">
                <a-select v-model:value="formData.clientServer" placeholder="选择模式">
                  <a-select-option value="client">客户端</a-select-option>
                  <a-select-option value="server">服务端</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="协议类型" name="modbusProtocol">
                <a-select v-model:value="formData.modbusProtocol" placeholder="选择协议">
                  <a-select-option value="RTU">RTU</a-select-option>
                  <a-select-option value="TCP">TCP</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="端口/URI" name="portUri">
                <a-input 
                  v-model:value="formData.portUri" 
                  :placeholder="getPortPlaceholder()"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="设备ID" name="deviceId">
                <a-input-number 
                  v-model:value="formData.deviceId" 
                  :min="1" 
                  :max="255"
                  placeholder="设备ID"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </template>

        <!-- 变量类型配置 -->
        <template v-if="formData.type === 'variable'">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="变量类型" name="variableType">
                <a-select v-model:value="formData.variableType" placeholder="选择变量类型">
                  <a-select-option value="number">数值</a-select-option>
                  <a-select-option value="text">文本</a-select-option>
                  <a-select-option value="bool">布尔</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="初始值" name="initialValue">
                <a-input 
                  v-model:value="formData.initialValue" 
                  placeholder="初始值"
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item label="单位" name="units">
            <a-input 
              v-model:value="formData.units" 
              placeholder="单位（可选）"
            />
          </a-form-item>
        </template>

        <!-- 分组类型配置 -->
        <template v-if="formData.type === 'group'">
          <a-form-item label="分组类型" name="groupType">
            <a-select v-model:value="formData.groupType" placeholder="选择分组类型">
              <a-select-option value="logical">逻辑分组</a-select-option>
              <a-select-option value="physical">物理分组</a-select-option>
              <a-select-option value="functional">功能分组</a-select-option>
            </a-select>
          </a-form-item>
        </template>

        <!-- 动作类型配置 -->
        <template v-if="formData.type === 'action'">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="动作类型" name="actionType">
                <a-select v-model:value="formData.actionType" placeholder="选择动作类型">
                  <a-select-option value="command">命令</a-select-option>
                  <a-select-option value="script">脚本</a-select-option>
                  <a-select-option value="notification">通知</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="触发条件" name="triggerCondition">
                <a-input 
                  v-model:value="formData.triggerCondition" 
                  placeholder="触发条件"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </template>
      </div>

      <!-- 标签配置 -->
      <a-form-item label="标签">
        <a-select
          v-model:value="formData.tags"
          mode="tags"
          placeholder="添加标签"
          :options="tagOptions"
        />
      </a-form-item>

      <!-- 是否禁用 -->
      <a-form-item name="disabled">
        <a-checkbox v-model:checked="formData.disabled">
          禁用此设备
        </a-checkbox>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  DatabaseOutlined,
  ApiOutlined,
  FunctionOutlined,
  GroupOutlined,
  PlayCircleOutlined
} from '@ant-design/icons-vue'
import { useNodeStore } from '@/stores/node'
import type { CreateNodeSendRequest } from '@/types/node'
import { NodeType } from '@/types/common'

interface Props {
  open: boolean
  defaultParent?: string  // 默认父节点ID
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'success', nodeId?: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const nodeStore = useNodeStore()

// 状态
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const loading = ref(false)
const formRef = ref()

// 表单数据
const formData = ref({
  type: '',
  parent: '',
  description: '',
  // 设备类型特定字段
  deviceAddress: '',
  protocol: '',
  // Modbus特定字段
  clientServer: '',
  modbusProtocol: '',
  portUri: '',
  deviceId: 1,
  // 变量特定字段
  variableType: '',
  initialValue: '',
  units: '',
  // 分组特定字段
  groupType: '',
  // 动作特定字段
  actionType: '',
  triggerCondition: '',
  // 通用字段
  tags: [] as string[],
  disabled: false
})

// 表单验证规则
const rules = {
  type: [
    { required: true, message: '请选择设备类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入设备描述', trigger: 'blur' }
  ]
}

// 父节点选项
const parentOptions = computed(() => {
  const nodes = nodeStore.nodes.data || []
  
  const buildOptions = (nodeList: any[], level = 0): any[] => {
    return nodeList
      .filter(node => ['group', 'device'].includes(node.type))
      .map(node => ({
        title: getNodeDescription(node),
        value: node.id,
        key: node.id
      }))
  }
  
  return [
    { title: '根节点', value: 'root', key: 'root' },
    ...buildOptions(nodes)
  ]
})

// 标签选项
const tagOptions = [
  { label: '生产', value: 'production' },
  { label: '测试', value: 'test' },
  { label: '重要', value: 'important' },
  { label: '监控', value: 'monitoring' }
]

// 获取节点描述
const getNodeDescription = (node: any): string => {
  const descPoint = node.points?.find((p: any) => p.type === 'description')
  return descPoint?.text || descPoint?.value || node.id
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    device: '设备',
    modbus: 'Modbus',
    variable: '变量',
    group: '分组',
    action: '动作'
  }
  return textMap[type] || type
}

// 获取端口占位符
const getPortPlaceholder = (): string => {
  if (formData.value.modbusProtocol === 'RTU') {
    return '/dev/ttyUSB0'
  } else if (formData.value.modbusProtocol === 'TCP') {
    return formData.value.clientServer === 'server' ? '502' : '*************:502'
  }
  return '端口或URI'
}

// 处理类型变化
const handleTypeChange = () => {
  // 清空类型特定的字段
  formData.value.deviceAddress = ''
  formData.value.protocol = ''
  formData.value.clientServer = ''
  formData.value.modbusProtocol = ''
  formData.value.portUri = ''
  formData.value.deviceId = 1
  formData.value.variableType = ''
  formData.value.initialValue = ''
  formData.value.units = ''
  formData.value.groupType = ''
  formData.value.actionType = ''
  formData.value.triggerCondition = ''
}

// 构建数据点
const buildPoints = () => {
  const points = []
  const currentTime = new Date().toISOString()

  // 基础描述点
  if (formData.value.description) {
    points.push({
      type: 'description',
      key: '0',
      time: currentTime,
      text: formData.value.description
    })
  }

  // 禁用状态点
  if (formData.value.disabled) {
    points.push({
      type: 'disabled',
      key: '0',
      value: 1,
      time: currentTime
    })
  }
  
  // 根据类型添加特定点
  switch (formData.value.type) {
    case 'modbus':
      if (formData.value.clientServer) {
        points.push({
          type: 'clientServer',
          key: '0',
          time: currentTime,
          text: formData.value.clientServer
        })
      }
      if (formData.value.modbusProtocol) {
        points.push({
          type: 'protocol',
          key: '0',
          time: currentTime,
          text: formData.value.modbusProtocol
        })
      }
      if (formData.value.portUri) {
        points.push({
          type: formData.value.modbusProtocol === 'TCP' ? 'uri' : 'port',
          key: '0',
          time: currentTime,
          text: formData.value.portUri
        })
      }
      if (formData.value.deviceId) {
        points.push({
          type: 'id',
          key: '0',
          value: formData.value.deviceId,
          time: currentTime
        })
      }
      break
      
    case 'variable':
      if (formData.value.variableType) {
        points.push({
          type: 'variableType',
          key: '0',
          time: currentTime,
          text: formData.value.variableType
        })
      }
      if (formData.value.initialValue) {
        points.push({
          type: 'value',
          key: '0',
          time: currentTime,
          text: formData.value.initialValue
        })
      }
      if (formData.value.units) {
        points.push({
          type: 'units',
          key: '0',
          time: currentTime,
          text: formData.value.units
        })
      }
      break
  }
  
  // 添加标签点
  formData.value.tags.forEach(tag => {
    points.push({
      type: 'tag',
      key: tag,
      time: currentTime,
      text: tag
    })
  })
  
  return points
}

// 处理提交
const handleSubmit = async () => {
  try {
    loading.value = true
    
    const createData: CreateNodeSendRequest = {
      type: formData.value.type as any,
      parent: formData.value.parent,
      points: buildPoints(),
      edgePoints: []  // 初始为空数组
    }
    
    const response = await nodeStore.createNode(createData)

    if (response.success) {
      message.success('设备创建成功')
      emit('success', response.id)
      handleCancel()
    } else {
      message.error('创建设备失败')
    }
  } catch (error) {
    console.error('创建设备失败:', error)
    message.error('创建设备失败')
  } finally {
    loading.value = false
  }
}

// 处理确定
const handleOk = async () => {
  try {
    await formRef.value?.validate()
    await handleSubmit()
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

// 处理取消
const handleCancel = () => {
  formRef.value?.resetFields()
  formData.value = {
    type: '',
    parent: '',
    description: '',
    deviceAddress: '',
    protocol: '',
    clientServer: '',
    modbusProtocol: '',
    portUri: '',
    deviceId: 1,
    variableType: '',
    initialValue: '',
    units: '',
    groupType: '',
    actionType: '',
    triggerCondition: '',
    tags: [],
    disabled: false
  }
  visible.value = false
}

// 监听打开状态，加载父节点数据
watch(visible, (newVisible) => {
  if (newVisible) {
    // 加载节点数据
    if (!nodeStore.nodes.data || nodeStore.nodes.data.length === 0) {
      nodeStore.fetchNodes()
    }

    // 设置默认父节点
    if (props.defaultParent) {
      formData.value.parent = props.defaultParent
    }
  }
})
</script>

<style scoped lang="less">
.type-specific-config {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>

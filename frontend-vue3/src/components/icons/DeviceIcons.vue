<template>
  <component 
    :is="iconComponent" 
    :class="['device-icon', `device-icon-${type}`, sizeClass]"
    :style="{ color: iconColor }"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  DatabaseOutlined,
  GroupOutlined,
  ApiOutlined,
  UsbOutlined,
  NodeIndexOutlined,
  UserOutlined,
  WifiOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  MonitorOutlined,
  CameraOutlined,
  BulbOutlined,
  FireOutlined,
  CloudOutlined,
  RobotOutlined,
  ToolOutlined,
  SafetyOutlined,
  ExperimentOutlined,
  RadarChartOutlined,
  SoundOutlined
} from '@ant-design/icons-vue'

// 组件属性
interface Props {
  type: string
  size?: 'small' | 'default' | 'large'
  color?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default'
})

// 设备类型图标映射
const deviceIconMap: Record<string, any> = {
  // 基础设备类型
  device: DatabaseOutlined,
  group: GroupOutlined,
  user: UserOutlined,
  
  // 通信协议类型
  modbus: ApiOutlined,
  serial: UsbOutlined,
  canbus: NodeIndexOutlined,
  wifi: WifiOutlined,
  ethernet: CloudOutlined,
  
  // 传感器类型
  temperature: FireOutlined,
  humidity: CloudOutlined,
  pressure: RadarChartOutlined,
  light: BulbOutlined,
  sound: SoundOutlined,
  motion: RobotOutlined,
  
  // 执行器类型
  switch: ThunderboltOutlined,
  relay: ThunderboltOutlined,
  valve: ToolOutlined,
  motor: SettingOutlined,
  
  // 监控设备
  camera: CameraOutlined,
  monitor: MonitorOutlined,
  alarm: SafetyOutlined,
  
  // 特殊设备
  gateway: WifiOutlined,
  controller: SettingOutlined,
  analyzer: ExperimentOutlined,
  
  // 默认图标
  default: DatabaseOutlined
}

// 设备类型颜色映射
const deviceColorMap: Record<string, string> = {
  // 传感器 - 蓝色系
  temperature: '#1890ff',
  humidity: '#13c2c2',
  pressure: '#722ed1',
  light: '#faad14',
  sound: '#52c41a',
  motion: '#eb2f96',
  
  // 执行器 - 紫色系
  switch: '#722ed1',
  relay: '#722ed1',
  valve: '#722ed1',
  motor: '#722ed1',
  
  // 通信设备 - 青色系
  modbus: '#13c2c2',
  serial: '#13c2c2',
  canbus: '#13c2c2',
  wifi: '#13c2c2',
  ethernet: '#13c2c2',
  gateway: '#13c2c2',
  
  // 监控设备 - 橙色系
  camera: '#fa8c16',
  monitor: '#fa8c16',
  alarm: '#ff4d4f',
  
  // 控制设备 - 绿色系
  controller: '#52c41a',
  analyzer: '#52c41a',
  
  // 分组和用户 - 灰色系
  group: '#8c8c8c',
  user: '#8c8c8c',
  
  // 默认颜色
  default: '#1890ff'
}

// 计算属性
const iconComponent = computed(() => {
  return deviceIconMap[props.type] || deviceIconMap.default
})

const iconColor = computed(() => {
  if (props.color) {
    return props.color
  }
  return deviceColorMap[props.type] || deviceColorMap.default
})

const sizeClass = computed(() => {
  return `size-${props.size}`
})

// 导出图标映射供其他组件使用
export const getDeviceIcon = (type: string) => {
  return deviceIconMap[type] || deviceIconMap.default
}

export const getDeviceColor = (type: string) => {
  return deviceColorMap[type] || deviceColorMap.default
}
</script>

<style scoped lang="less">
.device-icon {
  display: inline-block;
  
  &.size-small {
    font-size: 16px;
  }
  
  &.size-default {
    font-size: 20px;
  }
  
  &.size-large {
    font-size: 24px;
  }
  
  // 设备类型特殊样式
  &.device-icon-temperature {
    animation: temperature-pulse 3s ease-in-out infinite;
  }
  
  &.device-icon-switch,
  &.device-icon-relay {
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.1);
    }
  }
  
  &.device-icon-camera {
    animation: camera-blink 2s ease-in-out infinite;
  }
}

// 温度传感器脉冲动画
@keyframes temperature-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// 摄像头闪烁动画
@keyframes camera-blink {
  0%, 90%, 100% {
    opacity: 1;
  }
  95% {
    opacity: 0.5;
  }
}
</style>

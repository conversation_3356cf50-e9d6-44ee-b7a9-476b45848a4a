// Ant Design Vue 主题变量
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;
@info-color: #1890ff;

// 字体
@font-size-base: 14px;
@font-size-lg: 16px;
@font-size-sm: 12px;

// 间距
@padding-xs: 8px;
@padding-sm: 12px;
@padding-md: 16px;
@padding-lg: 24px;
@padding-xl: 32px;

// 边框
@border-radius-base: 6px;
@border-color-base: #d9d9d9;

// 阴影
@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);

// 布局
@layout-header-height: 64px;
@layout-sider-width: 256px;

// 自定义颜色
@node-device-color: #1890ff;
@node-modbus-color: #52c41a;
@node-variable-color: #faad14;
@node-group-color: #722ed1;
@node-action-color: #f5222d;

// 设备状态颜色系统
@status-online: #52c41a;    // 绿色 - 在线/运行中
@status-offline: #d9d9d9;   // 灰色 - 离线
@status-error: #ff4d4f;     // 红色 - 故障/错误
@status-warning: #faad14;   // 橙色 - 警告/未知

// 设备类型颜色系统
@device-sensor: #1890ff;    // 蓝色 - 传感器类设备
@device-actuator: #722ed1;  // 紫色 - 执行器类设备
@device-gateway: #13c2c2;   // 青色 - 网关通信设备
@device-controller: #52c41a; // 绿色 - 控制器设备
@device-monitor: #fa8c16;   // 橙色 - 监控设备
@device-group: #8c8c8c;     // 灰色 - 分组和用户

// 设备卡片样式变量
@device-card-padding: 16px;
@device-card-border-radius: 8px;
@device-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
@device-card-hover-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
@device-card-gap: 16px;

// 设备网格布局变量
@device-grid-columns-xs: 1;   // 超小屏幕
@device-grid-columns-sm: 2;   // 小屏幕
@device-grid-columns-md: 3;   // 中等屏幕
@device-grid-columns-lg: 4;   // 大屏幕
@device-grid-columns-xl: 5;   // 超大屏幕

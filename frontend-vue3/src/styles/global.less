// 全局样式
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  font-size: @font-size-base;
  line-height: 1.5715;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f0f2f5;
}

#app {
  min-height: 100vh;
}

// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: @padding-xs; }
.mb-2 { margin-bottom: @padding-sm; }
.mb-3 { margin-bottom: @padding-md; }
.mb-4 { margin-bottom: @padding-lg; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: @padding-xs; }
.mt-2 { margin-top: @padding-sm; }
.mt-3 { margin-top: @padding-md; }
.mt-4 { margin-top: @padding-lg; }

.p-0 { padding: 0; }
.p-1 { padding: @padding-xs; }
.p-2 { padding: @padding-sm; }
.p-3 { padding: @padding-md; }
.p-4 { padding: @padding-lg; }

// 节点相关样式
.node-card {
  margin-bottom: @padding-md;
  border-radius: @border-radius-base;
  box-shadow: @box-shadow-base;
  
  .node-header {
    display: flex;
    align-items: center;
    gap: @padding-xs;
    
    .node-icon {
      font-size: 16px;
    }
  }
  
  .node-content {
    padding: @padding-md 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    z-index: 999;
    height: 100vh;
  }
}

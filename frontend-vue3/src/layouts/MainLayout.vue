<template>
  <a-layout class="main-layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      :width="240"
      :collapsed-width="64"
      class="sidebar"
      theme="light"
    >
      <!-- Logo区域 -->
      <div class="logo-container">
        <div class="logo-content">
          <img src="/simple-iot-app-logo.png" alt="Simple IoT" class="logo-image" />
          <span v-show="!collapsed" class="logo-text">Simple IoT</span>
        </div>
      </div>

      <!-- 导航菜单 -->
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        theme="light"
        class="sidebar-menu"
        @click="handleMenuClick"
      >
        <!-- 设备管理 -->
        <a-menu-item key="device-tree" class="menu-item">
          <template #icon>
            <DatabaseOutlined />
          </template>
          设备管理
        </a-menu-item>

        <!-- 设备分组 -->
        <a-menu-item key="device-groups" class="menu-item">
          <template #icon>
            <GroupOutlined />
          </template>
          设备分组
        </a-menu-item>

        <!-- 监控中心 -->
        <a-sub-menu key="monitoring" class="menu-item">
          <template #icon>
            <MonitorOutlined />
          </template>
          <template #title>监控中心</template>
          <a-menu-item key="dashboard">
            <template #icon>
              <DashboardOutlined />
            </template>
            监控面板
          </a-menu-item>
          <a-menu-item key="real-time">
            <template #icon>
              <LineChartOutlined />
            </template>
            实时数据
          </a-menu-item>
          <a-menu-item key="history">
            <template #icon>
              <HistoryOutlined />
            </template>
            历史数据
          </a-menu-item>
          <a-menu-item key="alerts">
            <template #icon>
              <BellOutlined />
            </template>
            告警管理
          </a-menu-item>
        </a-sub-menu>

        <!-- 规则引擎 -->
        <a-sub-menu key="rules" class="menu-item">
          <template #icon>
            <SettingOutlined />
          </template>
          <template #title>规则引擎</template>
          <a-menu-item key="rule-list">
            <template #icon>
              <OrderedListOutlined />
            </template>
            规则列表
          </a-menu-item>
          <a-menu-item key="conditions">
            <template #icon>
              <BranchesOutlined />
            </template>
            条件配置
          </a-menu-item>
          <a-menu-item key="actions">
            <template #icon>
              <ThunderboltOutlined />
            </template>
            动作配置
          </a-menu-item>
        </a-sub-menu>

        <!-- 网络配置 -->
        <a-sub-menu key="network" class="menu-item">
          <template #icon>
            <GlobalOutlined />
          </template>
          <template #title>网络配置</template>
          <a-menu-item key="modbus">
            <template #icon>
              <ApiOutlined />
            </template>
            Modbus配置
          </a-menu-item>
          <a-menu-item key="serial">
            <template #icon>
              <UsbOutlined />
            </template>
            串口配置
          </a-menu-item>
          <a-menu-item key="canbus">
            <template #icon>
              <NodeIndexOutlined />
            </template>
            CAN总线
          </a-menu-item>
          <a-menu-item key="network-manager">
            <template #icon>
              <WifiOutlined />
            </template>
            网络管理
          </a-menu-item>
        </a-sub-menu>

        <!-- 系统管理 -->
        <a-sub-menu key="system" class="menu-item">
          <template #icon>
            <ControlOutlined />
          </template>
          <template #title>系统管理</template>
          <a-menu-item key="users">
            <template #icon>
              <UserOutlined />
            </template>
            用户管理
          </a-menu-item>
          <a-menu-item key="logs">
            <template #icon>
              <FileTextOutlined />
            </template>
            系统日志
          </a-menu-item>
          <a-menu-item key="backup">
            <template #icon>
              <CloudDownloadOutlined />
            </template>
            备份恢复
          </a-menu-item>
          <a-menu-item key="update">
            <template #icon>
              <CloudUploadOutlined />
            </template>
            系统更新
          </a-menu-item>
        </a-sub-menu>
      </a-menu>
    </a-layout-sider>

    <!-- 主内容区 -->
    <a-layout class="main-content" :style="{ marginLeft: collapsed ? '64px' : '240px' }">
      <!-- 顶部导航栏 -->
      <a-layout-header class="header">
        <div class="header-left">
          <!-- 折叠按钮 -->
          <a-button
            type="text"
            class="trigger"
            @click="toggleCollapsed"
          >
            <MenuUnfoldOutlined v-if="collapsed" />
            <MenuFoldOutlined v-else />
          </a-button>

          <!-- 面包屑导航 -->
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item v-for="item in breadcrumbItems" :key="item.path">
              <router-link v-if="item.path" :to="item.path">
                {{ item.title }}
              </router-link>
              <span v-else>{{ item.title }}</span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 主题切换 -->
          <a-tooltip title="切换主题">
            <a-button type="text" class="header-action" @click="toggleTheme">
              <BulbOutlined v-if="isDarkTheme" />
              <BulbFilled v-else />
            </a-button>
          </a-tooltip>

          <!-- 全屏切换 -->
          <a-tooltip title="全屏">
            <a-button type="text" class="header-action" @click="toggleFullscreen">
              <FullscreenOutlined />
            </a-button>
          </a-tooltip>

          <!-- 用户信息 -->
          <a-dropdown placement="bottomRight">
            <a-space class="user-info">
              <a-avatar :size="32">
                <template #icon>
                  <UserOutlined />
                </template>
              </a-avatar>
              <span class="username">{{ user?.email || '用户' }}</span>
              <DownOutlined />
            </a-space>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人资料
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  系统设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>

      <!-- 内容区域 -->
      <a-layout-content class="content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  DatabaseOutlined,
  MonitorOutlined,
  SettingOutlined,
  GlobalOutlined,
  ControlOutlined,
  UnorderedListOutlined,
  ApartmentOutlined,
  GroupOutlined,
  DashboardOutlined,
  LineChartOutlined,
  HistoryOutlined,
  BellOutlined,
  OrderedListOutlined,
  BranchesOutlined,
  ThunderboltOutlined,
  ApiOutlined,
  UsbOutlined,
  NodeIndexOutlined,
  WifiOutlined,
  UserOutlined,
  FileTextOutlined,
  CloudDownloadOutlined,
  CloudUploadOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  BulbOutlined,
  BulbFilled,
  FullscreenOutlined,
  DownOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useAuth } from '@/composables/useAuth'
import { useAppStore } from '@/stores/app'

// 状态管理
const appStore = useAppStore()
const { user, logout } = useAuth()
const route = useRoute()
const router = useRouter()

// 侧边栏状态
const collapsed = ref(false)
const selectedKeys = ref<string[]>([])
const openKeys = ref<string[]>([])

// 主题状态
const isDarkTheme = computed(() => appStore.theme === 'dark')

// 面包屑导航
const breadcrumbItems = computed(() => {
  const items = [{ title: '首页', path: '/' }]
  
  // 根据当前路由生成面包屑
  const pathSegments = route.path.split('/').filter(Boolean)
  let currentPath = ''
  
  pathSegments.forEach(segment => {
    currentPath += `/${segment}`
    const title = getPageTitle(currentPath)
    if (title) {
      items.push({ title, path: currentPath })
    }
  })
  
  return items
})

// 获取页面标题
const getPageTitle = (path: string): string => {
  const titleMap: Record<string, string> = {
    '/devices': '设备管理',
    '/devices/tree': '设备管理',
    '/devices/groups': '设备分组',
    '/monitoring': '监控中心',
    '/monitoring/dashboard': '监控面板',
    '/monitoring/real-time': '实时数据',
    '/monitoring/history': '历史数据',
    '/monitoring/alerts': '告警管理',
    '/rules': '规则引擎',
    '/rules/list': '规则列表',
    '/rules/conditions': '条件配置',
    '/rules/actions': '动作配置',
    '/network': '网络配置',
    '/network/modbus': 'Modbus配置',
    '/network/serial': '串口配置',
    '/network/canbus': 'CAN总线',
    '/network/manager': '网络管理',
    '/system': '系统管理',
    '/system/users': '用户管理',
    '/system/logs': '系统日志',
    '/system/backup': '备份恢复',
    '/system/update': '系统更新'
  }
  return titleMap[path] || ''
}

// 更新菜单状态
const updateMenuState = (path: string) => {
  // 根据路径设置选中的菜单项
  if (path.startsWith('/devices/tree') || path === '/devices') {
    selectedKeys.value = ['device-tree']
  } else if (path.startsWith('/devices/groups')) {
    selectedKeys.value = ['device-groups']
  } else if (path.startsWith('/monitoring')) {
    selectedKeys.value = [path.replace('/monitoring/', '').replace('/monitoring', 'dashboard')]
    openKeys.value = ['monitoring']
  } else if (path.startsWith('/rules')) {
    selectedKeys.value = [path.replace('/rules/', 'rule-').replace('/rules', 'rule-list')]
    openKeys.value = ['rules']
  } else if (path.startsWith('/network')) {
    selectedKeys.value = [path.replace('/network/', '').replace('/network', 'modbus')]
    openKeys.value = ['network']
  } else if (path.startsWith('/system')) {
    selectedKeys.value = [path.replace('/system/', '').replace('/system', 'users')]
    openKeys.value = ['system']
  }
}

// 监听路由变化，更新菜单选中状态
watch(() => route.path, (newPath) => {
  updateMenuState(newPath)
}, { immediate: true })

// 切换侧边栏折叠状态
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
  appStore.setSidebarCollapsed(collapsed.value)
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  const routeMap: Record<string, string> = {
    'device-tree': '/devices/tree',
    'device-groups': '/devices/groups',
    'dashboard': '/monitoring/dashboard',
    'real-time': '/monitoring/real-time',
    'history': '/monitoring/history',
    'alerts': '/monitoring/alerts',
    'rule-list': '/rules/list',
    'conditions': '/rules/conditions',
    'actions': '/rules/actions',
    'modbus': '/network/modbus',
    'serial': '/network/serial',
    'canbus': '/network/canbus',
    'network-manager': '/network/manager',
    'users': '/system/users',
    'logs': '/system/logs',
    'backup': '/system/backup',
    'update': '/system/update'
  }
  
  const targetRoute = routeMap[key]
  if (targetRoute) {
    router.push(targetRoute)
  }
}

// 切换主题
const toggleTheme = () => {
  const newTheme = isDarkTheme.value ? 'light' : 'dark'
  appStore.setTheme(newTheme)
}

// 切换全屏
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

// 处理登出
const handleLogout = async () => {
  try {
    await logout()
    message.success('已退出登录')
  } catch (err) {
    message.error('退出登录失败')
  }
}

// 初始化侧边栏状态
collapsed.value = appStore.sidebarCollapsed
</script>

<style scoped lang="less">
.main-layout {
  min-height: 100vh;

  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 100;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

    .logo-container {
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 8px;

      .logo-content {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 0 16px;

        .logo-image {
          width: 32px;
          height: 32px;
          flex-shrink: 0;
        }

        .logo-text {
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
          white-space: nowrap;
        }
      }
    }

    .sidebar-menu {
      border-right: none;
      height: calc(100vh - 72px);
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: #d9d9d9;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #bfbfbf;
      }

      .menu-item {
        .ant-menu-submenu-title {
          font-weight: 500;
        }
      }
    }
  }

  .main-content {
    margin-left: 240px;
    transition: margin-left 0.2s;

    &.collapsed {
      margin-left: 64px;
    }

    // 根据侧边栏状态调整边距
    &:has(+ .ant-layout-sider-collapsed) {
      margin-left: 64px;
    }

    .header {
      background: white;
      padding: 0 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: sticky;
      top: 0;
      z-index: 99;

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        .trigger {
          font-size: 18px;
          line-height: 64px;
          cursor: pointer;
          transition: color 0.3s;

          &:hover {
            color: @primary-color;
          }
        }

        .breadcrumb {
          .ant-breadcrumb-link {
            color: #6b7280;

            &:hover {
              color: @primary-color;
            }
          }

          .ant-breadcrumb-separator {
            color: #d1d5db;
          }
        }
      }

      .header-right {
        display: flex;
        align-items: center;
        gap: 8px;

        .header-action {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 6px;
          transition: all 0.3s;

          &:hover {
            background: #f5f5f5;
            color: @primary-color;
          }
        }

        .user-info {
          padding: 8px 12px;
          border-radius: 6px;
          cursor: pointer;
          transition: background 0.3s;

          &:hover {
            background: #f5f5f5;
          }

          .username {
            color: #374151;
            font-weight: 500;
          }
        }
      }
    }

    .content {
      margin: 24px;
      min-height: calc(100vh - 112px);

      .content-wrapper {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        min-height: 100%;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .main-layout {
    .sidebar {
      transform: translateX(-100%);
      transition: transform 0.3s;

      &.mobile-open {
        transform: translateX(0);
      }
    }

    .main-content {
      margin-left: 0 !important;

      .header {
        padding: 0 16px;

        .header-left {
          gap: 8px;
        }

        .breadcrumb {
          display: none;
        }
      }

      .content {
        margin: 16px;
      }
    }
  }
}

// 暗色主题
[data-theme='dark'] {
  .main-layout {
    .sidebar {
      background: #141414;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.3);

      .logo-container {
        border-bottom-color: #303030;

        .logo-text {
          color: #ffffff;
        }
      }

      .sidebar-menu {
        background: #141414;
        color: #ffffff;
      }
    }

    .main-content {
      .header {
        background: #1f1f1f;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

        .header-left {
          .trigger:hover {
            color: @primary-color;
          }

          .breadcrumb {
            .ant-breadcrumb-link {
              color: #d1d5db;

              &:hover {
                color: @primary-color;
              }
            }
          }
        }

        .header-right {
          .header-action:hover {
            background: #303030;
          }

          .user-info {
            &:hover {
              background: #303030;
            }

            .username {
              color: #ffffff;
            }
          }
        }
      }

      .content {
        .content-wrapper {
          background: #1f1f1f;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}
</style>

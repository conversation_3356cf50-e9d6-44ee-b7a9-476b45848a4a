// 认证组合式 API
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import type { LoginRequest } from '@/types/auth'

export const useAuth = () => {
  const authStore = useAuthStore()
  const router = useRouter()

  // 状态
  const user = computed(() => authStore.user)
  const isAuthenticated = computed(() => authStore.isAuthenticated)
  const isLoading = computed(() => authStore.isLoading)
  const error = computed(() => authStore.error)
  const token = computed(() => authStore.token)

  // 登录
  const login = async (credentials: LoginRequest) => {
    try {
      await authStore.login(credentials)
      // 登录成功后重定向到首页
      await router.push('/')
    } catch (err) {
      // 错误已经在 store 中处理
      throw err
    }
  }

  // 登出
  const logout = async () => {
    await authStore.logout()
    // 登出后重定向到登录页
    await router.push('/login')
  }

  // 检查认证状态
  const checkAuth = async () => {
    if (!isAuthenticated.value) {
      await router.push('/login')
      return false
    }

    // 验证 token 有效性
    const isValid = await authStore.validateToken()
    if (!isValid) {
      await router.push('/login')
      return false
    }

    return true
  }

  // 需要认证的路由守卫
  const requireAuth = async () => {
    const isValid = await checkAuth()
    if (!isValid) {
      throw new Error('Authentication required')
    }
  }

  // 清除错误
  const clearError = () => {
    authStore.clearError()
  }

  // 初始化认证状态
  const initAuth = () => {
    authStore.initAuth()
  }

  return {
    // 状态
    user,
    isAuthenticated,
    isLoading,
    error,
    token,

    // 方法
    login,
    logout,
    checkAuth,
    requireAuth,
    clearError,
    initAuth
  }
}

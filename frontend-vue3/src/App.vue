<template>
  <div id="app">
    <a-config-provider :locale="locale">
      <router-view />
    </a-config-provider>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import enUS from 'ant-design-vue/es/locale/en_US'
import { useAppStore } from '@/stores/app'
import { useAuthStore } from '@/stores/auth'

// 状态管理
const appStore = useAppStore()
const authStore = useAuthStore()

// 国际化配置
const locale = computed(() => {
  return appStore.language === 'zh-CN' ? zhCN : enUS
})

// 应用初始化
onMounted(() => {
  // 初始化应用状态
  appStore.initApp()
  
  // 初始化认证状态
  authStore.initAuth()
})
</script>

<style lang="less">
// 导入全局样式
@import '@/styles/global.less';

#app {
  min-height: 100vh;
}
</style>

<template>
  <div class="rule-list-page">
    <a-card title="规则列表" class="page-card">
      <template #extra>
        <a-button type="primary">
          <template #icon>
            <PlusOutlined />
          </template>
          新建规则
        </a-button>
      </template>
      
      <a-empty description="规则列表功能开发中...">
        <template #image>
          <OrderedListOutlined style="font-size: 64px; color: #d9d9d9;" />
        </template>
      </a-empty>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { PlusOutlined, OrderedListOutlined } from '@ant-design/icons-vue'
</script>

<style scoped lang="less">
.rule-list-page {
  padding: 24px;
}
</style>

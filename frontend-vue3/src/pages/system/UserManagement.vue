<template>
  <div class="user-management-page">
    <a-card title="用户管理" class="page-card">
      <template #extra>
        <a-button type="primary">
          <template #icon>
            <PlusOutlined />
          </template>
          新建用户
        </a-button>
      </template>
      
      <a-empty description="用户管理功能开发中...">
        <template #image>
          <UserOutlined style="font-size: 64px; color: #d9d9d9;" />
        </template>
      </a-empty>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { PlusOutlined, UserOutlined } from '@ant-design/icons-vue'
</script>

<style scoped lang="less">
.user-management-page {
  padding: 24px;
}
</style>

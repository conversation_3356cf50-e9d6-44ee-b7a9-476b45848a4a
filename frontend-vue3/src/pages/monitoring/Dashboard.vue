<template>
  <div class="dashboard-page">
    <!-- 统计卡片 -->
    <a-row :gutter="16" class="stats-row">
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="设备总数"
            :value="stats.totalDevices"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <DatabaseOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="在线设备"
            :value="stats.onlineDevices"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <CheckCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="离线设备"
            :value="stats.offlineDevices"
            :value-style="{ color: '#ff4d4f' }"
          >
            <template #prefix>
              <CloseCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="告警数量"
            :value="stats.alertCount"
            :value-style="{ color: '#fa8c16' }"
          >
            <template #prefix>
              <BellOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="16" class="charts-row">
      <!-- 设备状态分布 -->
      <a-col :span="12">
        <a-card title="设备状态分布" class="chart-card">
          <template #extra>
            <a-button type="text" size="small" @click="refreshDeviceStatus">
              <ReloadOutlined />
            </a-button>
          </template>
          <div ref="deviceStatusChart" class="chart-container"></div>
        </a-card>
      </a-col>

      <!-- 数据传输趋势 -->
      <a-col :span="12">
        <a-card title="数据传输趋势" class="chart-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="timeRange" size="small" style="width: 100px">
                <a-select-option value="1h">1小时</a-select-option>
                <a-select-option value="6h">6小时</a-select-option>
                <a-select-option value="24h">24小时</a-select-option>
              </a-select>
              <a-button type="text" size="small" @click="refreshDataTrend">
                <ReloadOutlined />
              </a-button>
            </a-space>
          </template>
          <div ref="dataTrendChart" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 实时数据和告警 -->
    <a-row :gutter="16" class="data-row">
      <!-- 实时数据 -->
      <a-col :span="16">
        <a-card title="实时数据" class="data-card">
          <template #extra>
            <a-space>
              <a-switch
                v-model:checked="autoRefresh"
                checked-children="自动"
                un-checked-children="手动"
                @change="handleAutoRefreshChange"
              />
              <a-button type="text" size="small" @click="refreshRealTimeData">
                <ReloadOutlined />
              </a-button>
            </a-space>
          </template>
          
          <a-table
            :columns="dataColumns"
            :data-source="realTimeData"
            :pagination="false"
            size="small"
            :scroll="{ y: 300 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-badge
                  :status="record.status === 'normal' ? 'success' : 'error'"
                  :text="record.status === 'normal' ? '正常' : '异常'"
                />
              </template>
              <template v-else-if="column.key === 'value'">
                <span :class="{ 'warning-value': record.isWarning }">
                  {{ record.value }} {{ record.unit }}
                </span>
              </template>
              <template v-else-if="column.key === 'updateTime'">
                {{ formatTime(record.updateTime) }}
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>

      <!-- 最新告警 -->
      <a-col :span="8">
        <a-card title="最新告警" class="alert-card">
          <template #extra>
            <a-button type="text" size="small" @click="viewAllAlerts">
              查看全部
            </a-button>
          </template>
          
          <a-list
            :data-source="recentAlerts"
            size="small"
            :pagination="false"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-badge
                      :status="getAlertStatus(item.level)"
                      :text="getAlertLevelText(item.level)"
                    />
                  </template>
                  <template #title>
                    <div class="alert-title">{{ item.title }}</div>
                  </template>
                  <template #description>
                    <div class="alert-description">
                      <div>{{ item.description }}</div>
                      <div class="alert-time">{{ formatTime(item.time) }}</div>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>

    <!-- 设备快速操作 -->
    <a-row :gutter="16" class="actions-row">
      <a-col :span="24">
        <a-card title="设备快速操作" class="actions-card">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-card size="small" class="action-item" @click="handleQuickAction('restart')">
                <div class="action-content">
                  <ReloadOutlined class="action-icon" />
                  <div class="action-text">重启设备</div>
                </div>
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card size="small" class="action-item" @click="handleQuickAction('backup')">
                <div class="action-content">
                  <CloudDownloadOutlined class="action-icon" />
                  <div class="action-text">备份配置</div>
                </div>
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card size="small" class="action-item" @click="handleQuickAction('sync')">
                <div class="action-content">
                  <SyncOutlined class="action-icon" />
                  <div class="action-text">同步数据</div>
                </div>
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card size="small" class="action-item" @click="handleQuickAction('update')">
                <div class="action-content">
                  <CloudUploadOutlined class="action-icon" />
                  <div class="action-text">系统更新</div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import {
  DatabaseOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  BellOutlined,
  ReloadOutlined,
  CloudDownloadOutlined,
  SyncOutlined,
  CloudUploadOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { format } from 'date-fns'

// 页面状态
const autoRefresh = ref(true)
const timeRange = ref('1h')
const refreshTimer = ref<NodeJS.Timeout>()

// 统计数据
const stats = ref({
  totalDevices: 156,
  onlineDevices: 142,
  offlineDevices: 14,
  alertCount: 8
})

// 实时数据表格列
const dataColumns = [
  { title: '设备名称', dataIndex: 'deviceName', key: 'deviceName', width: 120 },
  { title: '数据点', dataIndex: 'pointName', key: 'pointName', width: 100 },
  { title: '当前值', dataIndex: 'value', key: 'value', width: 100 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 80 },
  { title: '更新时间', dataIndex: 'updateTime', key: 'updateTime', width: 140 }
]

// 实时数据
const realTimeData = ref([
  {
    key: '1',
    deviceName: '温度传感器-01',
    pointName: '温度',
    value: '25.6',
    unit: '°C',
    status: 'normal',
    isWarning: false,
    updateTime: new Date()
  },
  {
    key: '2',
    deviceName: '温度传感器-01',
    pointName: '湿度',
    value: '65.2',
    unit: '%',
    status: 'normal',
    isWarning: false,
    updateTime: new Date()
  },
  {
    key: '3',
    deviceName: '压力传感器-01',
    pointName: '压力',
    value: '1.8',
    unit: 'MPa',
    status: 'error',
    isWarning: true,
    updateTime: new Date(Date.now() - 300000)
  },
  {
    key: '4',
    deviceName: 'Modbus网关',
    pointName: '连接数',
    value: '5',
    unit: '个',
    status: 'normal',
    isWarning: false,
    updateTime: new Date()
  }
])

// 最新告警
const recentAlerts = ref([
  {
    id: '1',
    title: '压力传感器异常',
    description: '压力值超出正常范围',
    level: 'error',
    time: new Date(Date.now() - 300000)
  },
  {
    id: '2',
    title: '设备离线',
    description: '温度传感器-02失去连接',
    level: 'warning',
    time: new Date(Date.now() - 600000)
  },
  {
    id: '3',
    title: '数据延迟',
    description: 'Modbus网关数据更新延迟',
    level: 'info',
    time: new Date(Date.now() - 900000)
  }
])

// 图表引用
const deviceStatusChart = ref()
const dataTrendChart = ref()

// 格式化时间
const formatTime = (time: Date) => {
  return format(time, 'HH:mm:ss')
}

// 获取告警状态
const getAlertStatus = (level: string) => {
  const statusMap: Record<string, string> = {
    error: 'error',
    warning: 'warning',
    info: 'processing'
  }
  return statusMap[level] || 'default'
}

// 获取告警级别文本
const getAlertLevelText = (level: string) => {
  const textMap: Record<string, string> = {
    error: '严重',
    warning: '警告',
    info: '信息'
  }
  return textMap[level] || level
}

// 刷新设备状态图表
const refreshDeviceStatus = () => {
  message.success('设备状态图表已刷新')
  // 这里应该调用图表刷新逻辑
}

// 刷新数据趋势图表
const refreshDataTrend = () => {
  message.success('数据趋势图表已刷新')
  // 这里应该调用图表刷新逻辑
}

// 刷新实时数据
const refreshRealTimeData = () => {
  // 模拟数据更新
  realTimeData.value.forEach(item => {
    if (item.pointName === '温度') {
      item.value = (Math.random() * 10 + 20).toFixed(1)
    } else if (item.pointName === '湿度') {
      item.value = (Math.random() * 20 + 50).toFixed(1)
    }
    item.updateTime = new Date()
  })
  
  // 更新统计数据
  stats.value.onlineDevices = Math.floor(Math.random() * 10) + 140
  stats.value.offlineDevices = stats.value.totalDevices - stats.value.onlineDevices
}

// 自动刷新切换
const handleAutoRefreshChange = (checked: boolean) => {
  if (checked) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

// 开始自动刷新
const startAutoRefresh = () => {
  refreshTimer.value = setInterval(() => {
    refreshRealTimeData()
  }, 5000) // 每5秒刷新一次
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = undefined
  }
}

// 查看所有告警
const viewAllAlerts = () => {
  message.info('跳转到告警管理页面')
}

// 快速操作
const handleQuickAction = (action: string) => {
  const actionMap: Record<string, string> = {
    restart: '重启设备',
    backup: '备份配置',
    sync: '同步数据',
    update: '系统更新'
  }
  message.info(`执行操作: ${actionMap[action]}`)
}

// 初始化图表
const initCharts = () => {
  // 这里应该初始化ECharts或其他图表库
  // 由于没有引入图表库，这里只是占位
  console.log('初始化图表')
}

// 组件挂载
onMounted(() => {
  initCharts()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

// 组件卸载
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped lang="less">
.dashboard-page {
  padding: 24px;
  
  .stats-row {
    margin-bottom: 16px;
    
    .stat-card {
      text-align: center;
      
      .ant-statistic-title {
        margin-bottom: 8px;
      }
    }
  }
  
  .charts-row {
    margin-bottom: 16px;
    
    .chart-card {
      height: 350px;
      
      .chart-container {
        height: 280px;
        width: 100%;
      }
    }
  }
  
  .data-row {
    margin-bottom: 16px;
    
    .data-card {
      height: 400px;
      
      .warning-value {
        color: #ff4d4f;
        font-weight: bold;
      }
    }
    
    .alert-card {
      height: 400px;
      
      .alert-title {
        font-size: 14px;
        font-weight: 500;
      }
      
      .alert-description {
        .alert-time {
          color: #8c8c8c;
          font-size: 12px;
          margin-top: 4px;
        }
      }
    }
  }
  
  .actions-row {
    .actions-card {
      .action-item {
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }
        
        .action-content {
          text-align: center;
          padding: 16px;
          
          .action-icon {
            font-size: 24px;
            color: @primary-color;
            margin-bottom: 8px;
          }
          
          .action-text {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }
  }
}
</style>

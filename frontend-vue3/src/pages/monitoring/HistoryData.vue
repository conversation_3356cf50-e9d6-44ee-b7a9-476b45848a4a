<template>
  <div class="history-data-page">
    <a-card title="历史数据" class="page-card">
      <a-empty description="历史数据功能开发中...">
        <template #image>
          <HistoryOutlined style="font-size: 64px; color: #d9d9d9;" />
        </template>
      </a-empty>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { HistoryOutlined } from '@ant-design/icons-vue'
</script>

<style scoped lang="less">
.history-data-page {
  padding: 24px;
}
</style>

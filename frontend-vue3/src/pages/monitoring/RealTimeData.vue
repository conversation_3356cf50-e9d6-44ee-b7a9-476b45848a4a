<template>
  <div class="real-time-data-page">
    <a-card title="实时数据" class="page-card">
      <a-empty description="实时数据功能开发中...">
        <template #image>
          <LineChartOutlined style="font-size: 64px; color: #d9d9d9;" />
        </template>
      </a-empty>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { LineChartOutlined } from '@ant-design/icons-vue'
</script>

<style scoped lang="less">
.real-time-data-page {
  padding: 24px;
}
</style>

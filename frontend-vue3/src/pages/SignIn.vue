<template>
  <div class="signin-container">
    <div class="signin-card">
      <div class="signin-header">
        <img src="/simple-iot-app-logo.png" alt="Simple IoT" class="logo" />
        <h1>Simple IoT</h1>
        <p>物联网设备管理系统</p>
      </div>

      <a-form
        :model="formData"
        :rules="rules"
        @finish="handleSubmit"
        @finishFailed="handleSubmitFailed"
        layout="vertical"
        class="signin-form"
      >
        <a-form-item label="用户名/邮箱" name="email">
          <a-input
            v-model:value="formData.email"
            placeholder="请输入用户名或邮箱"
            size="large"
            :disabled="isLoading"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item label="密码" name="password">
          <a-input-password
            v-model:value="formData.password"
            placeholder="请输入密码"
            size="large"
            :disabled="isLoading"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>

        <a-form-item v-if="error" class="error-message">
          <a-alert :message="error" type="error" show-icon closable @close="clearError" />
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            :loading="isLoading"
            block
          >
            登录
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useAuth } from '@/composables/useAuth'
import type { LoginRequest } from '@/types/auth'

// 认证相关
const { login, isLoading, error, clearError, isAuthenticated } = useAuth()

// 表单数据
const formData = reactive<LoginRequest>({
  email: '',
  password: ''
})

// 表单验证规则
const rules = {
  email: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
    { min: 1, message: '用户名或邮箱不能为空', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 1, message: '密码不能为空', trigger: 'blur' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  try {
    await login(formData)
    message.success('登录成功')
  } catch (err) {
    // 错误已经在 useAuth 中处理
  }
}

// 表单验证失败
const handleSubmitFailed = () => {
  message.error('请检查输入信息')
}

// 监听认证状态变化
watch(isAuthenticated, (newValue) => {
  if (newValue) {
    // 已经在 useAuth 中处理重定向
  }
})
</script>

<style scoped lang="less">
.signin-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: @padding-lg;
}

.signin-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: @border-radius-base * 2;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: @padding-xl;
}

.signin-header {
  text-align: center;
  margin-bottom: @padding-xl;

  .logo {
    width: 64px;
    height: 64px;
    margin-bottom: @padding-md;
  }

  h1 {
    margin: 0 0 @padding-xs 0;
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
  }

  p {
    margin: 0;
    color: #6b7280;
    font-size: @font-size-sm;
  }
}

.signin-form {
  .error-message {
    margin-bottom: @padding-md;
  }
}

@media (max-width: 480px) {
  .signin-container {
    padding: @padding-md;
  }

  .signin-card {
    padding: @padding-lg;
  }
}
</style>

<template>
  <div class="device-groups-page">
    <a-card title="设备分组" class="page-card">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAddGroup">
            <template #icon>
              <PlusOutlined />
            </template>
            新建分组
          </a-button>
        </a-space>
      </template>

      <a-empty description="设备分组功能开发中...">
        <template #image>
          <GroupOutlined style="font-size: 64px; color: #d9d9d9;" />
        </template>
      </a-empty>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { PlusOutlined, GroupOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

const handleAddGroup = () => {
  message.info('新建分组功能开发中...')
}
</script>

<style scoped lang="less">
.device-groups-page {
  padding: 24px;
}
</style>

<template>
  <div class="device-tree-page">
    <a-row :gutter="16">
      <!-- 左侧设备树 -->
      <a-col :span="8">
        <a-card title="设备树" class="tree-card">
          <template #extra>
            <a-space>
              <a-button type="primary" size="small" @click="handleAddDevice">
                <template #icon>
                  <PlusOutlined />
                </template>
                添加设备
              </a-button>
              <a-button type="text" size="small" @click="handleExpandAll">
                <template #icon>
                  <PlusSquareOutlined />
                </template>
                展开全部
              </a-button>
              <a-button type="text" size="small" @click="handleCollapseAll">
                <template #icon>
                  <MinusSquareOutlined />
                </template>
                收起全部
              </a-button>
            </a-space>
          </template>

          <div class="tree-search">
            <a-input-search
              v-model:value="searchValue"
              placeholder="搜索设备"
              @search="handleSearch"
              allow-clear
            />
          </div>

          <a-tree
            v-model:expandedKeys="expandedKeys"
            v-model:selectedKeys="selectedKeys"
            :tree-data="treeData"
            :field-names="{ children: 'children', title: 'title', key: 'key' }"
            show-icon
            @select="handleNodeSelect"
            @right-click="handleRightClick"
            class="device-tree"
          >
            <template #icon="{ dataRef }">
              <component :is="getNodeIcon(dataRef.type)" />
            </template>
            <template #title="{ dataRef }">
              <div class="tree-node-title">
                <span>{{ dataRef.title }}</span>
                <a-tag v-if="dataRef.status" :color="getStatusColor(dataRef.status)" size="small">
                  {{ getStatusText(dataRef.status) }}
                </a-tag>
              </div>
            </template>
          </a-tree>
        </a-card>
      </a-col>

      <!-- 右侧设备详情 -->
      <a-col :span="16">
        <a-card v-if="selectedNode" :title="selectedNode.title" class="detail-card">
          <template #extra>
            <a-space>
              <a-button type="primary" @click="handleEdit">
                <template #icon>
                  <EditOutlined />
                </template>
                编辑
              </a-button>
              <a-button @click="handleAddChildDevice">
                <template #icon>
                  <PlusOutlined />
                </template>
                添加子设备
              </a-button>

              <a-dropdown>
                <a-button>
                  更多
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu @click="handleMenuClick">
                    <a-menu-item key="copy">
                      <CopyOutlined />
                      复制
                    </a-menu-item>
                    <a-menu-item key="move">
                      <DragOutlined />
                      移动
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" danger>
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>

          <!-- 基本信息 -->
          <a-descriptions title="基本信息" :column="2" bordered>
            <a-descriptions-item label="设备ID">
              {{ selectedNode.key }}
            </a-descriptions-item>
            <a-descriptions-item label="设备类型">
              <a-tag color="blue">{{ getTypeText(selectedNode.type) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="设备状态">
              <a-tag :color="getStatusColor(selectedNode.status)">
                {{ getStatusText(selectedNode.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="父节点">
              {{ selectedNode.raw?.parent || '根节点' }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatTime(selectedNode.createTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="最后更新">
              {{ formatTime(selectedNode.updateTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="描述" :span="2">
              {{ selectedNode.description || '暂无描述' }}
            </a-descriptions-item>
          </a-descriptions>

          <!-- 子节点列表 -->
          <div v-if="selectedNode.children && selectedNode.children.length > 0" class="children-section">
            <a-divider>子节点 ({{ selectedNode.children.length }})</a-divider>
            <a-list
              :data-source="selectedNode.children"
              size="small"
              :pagination="false"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <component :is="getNodeIcon(item.type)" />
                    </template>
                    <template #title>
                      <a @click="handleNodeSelect([item.key])">{{ item.title }}</a>
                    </template>
                    <template #description>
                      {{ item.description }}
                    </template>
                  </a-list-item-meta>
                  <template #actions>
                    <a-tag :color="getStatusColor(item.status)" size="small">
                      {{ getStatusText(item.status) }}
                    </a-tag>
                  </template>
                </a-list-item>
              </template>
            </a-list>
          </div>

          <!-- 数据点信息 -->
          <div v-if="filteredPoints.length > 0" class="points-section">
            <a-divider>数据点 ({{ filteredPoints.length }})</a-divider>
            <a-table
              :columns="pointColumns"
              :data-source="filteredPoints"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'value'">
                  <span v-if="record.value !== undefined">{{ record.value }}</span>
                  <span v-else>—</span>
                </template>
                <template v-else-if="column.key === 'text'">
                  <span v-if="record.text">{{ record.text }}</span>
                  <span v-else>—</span>
                </template>
                <template v-else-if="column.key === 'updateTime'">
                  {{ formatTime(record.time) }}
                </template>
              </template>
            </a-table>
          </div>
        </a-card>

        <!-- 未选择节点时的提示 -->
        <a-card v-else class="detail-card">
          <a-empty description="请从左侧设备树中选择一个节点查看详情">
            <template #image>
              <ApartmentOutlined style="font-size: 64px; color: #d9d9d9;" />
            </template>
          </a-empty>
        </a-card>
      </a-col>
    </a-row>

 

    <!-- 添加设备模态框 -->
    <AddDeviceModal
      v-model:open="addDeviceModalVisible"
      :default-parent="defaultParentForAdd"
      @success="handleAddDeviceSuccess"
    />

    <!-- 编辑设备模态框 -->
    <EditDeviceModal
      v-model:open="editDeviceModalVisible"
      :node="editingNode"
      @success="handleEditDeviceSuccess"
    />


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  PlusSquareOutlined,
  MinusSquareOutlined,
  EditOutlined,

  DownOutlined,
  CopyOutlined,
  DragOutlined,
  DeleteOutlined,
  ApartmentOutlined,
  PlusOutlined,
  DatabaseOutlined,
  ApiOutlined,
  UsbOutlined,
  NodeIndexOutlined,
  GroupOutlined,
  UserOutlined
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { format } from 'date-fns'
import { useNodeStore } from '@/stores/node'
import type { Node } from '@/types/node'
import AddDeviceModal from '@/components/devices/AddDeviceModal.vue'
import EditDeviceModal from '@/components/devices/EditDeviceModal.vue'


// 使用节点store
const nodeStore = useNodeStore()

// 页面状态
const searchValue = ref('')
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const contextMenuVisible = ref(false)
const rightClickNode = ref<any>(null)

// 模态框状态
const addDeviceModalVisible = ref(false)
const editDeviceModalVisible = ref(false)
const editingNode = ref<Node | null>(null)
const defaultParentForAdd = ref<string | undefined>(undefined)


// 获取节点描述
const getNodeDescription = (node: Node): string => {
  const descPoint = node.points.find(p => p.type === 'description')
  return descPoint?.text || descPoint?.value?.toString() || node.id
}

// 获取节点状态
const getNodeStatus = (node: Node): string => {
  const statePoint = node.points.find(p => p.type === 'sysState')
  if (statePoint) {
    switch (statePoint.text || statePoint.value) {
      case 'online':
        return 'online'
      case 'offline':
        return 'offline'
      case 'powerOff':
        return 'offline'
      default:
        return 'error'
    }
  }
  return 'offline'
}

// 构建树形数据
const buildTreeData = (nodes: Node[]): any[] => {
  const nodeMap = new Map<string, any>()
  const rootNodes: any[] = []

  // 创建节点映射
  nodes.forEach(node => {
    nodeMap.set(node.id, {
      key: node.id,
      title: getNodeDescription(node),
      type: node.type,
      status: getNodeStatus(node),
      description: getNodeDescription(node),
      createTime: new Date(),
      updateTime: new Date(),
      points: node.points,
      children: [],
      raw: node  // 保存原始节点数据，包含parent信息
    })
  })

  // 构建树形结构
  nodes.forEach(node => {
    const nodeView = nodeMap.get(node.id)!

    if (node.parent && nodeMap.has(node.parent)) {
      const parent = nodeMap.get(node.parent)!
      parent.children.push(nodeView)
    } else {
      rootNodes.push(nodeView)
    }
  })

  return rootNodes
}

// 树形数据
const treeData = computed(() => {
  const nodes = nodeStore.nodes.data || []
  return buildTreeData(nodes)
})

// 数据点表格列
const pointColumns = [
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: '键值', dataIndex: 'key', key: 'key', width: 80 },
  { title: '数值', dataIndex: 'value', key: 'value', width: 100 },
  { title: '文本', dataIndex: 'text', key: 'text' },
  { title: '更新时间', dataIndex: 'time', key: 'updateTime', width: 180 }
]

// 当前选中的节点
const selectedNode = computed(() => {
  if (selectedKeys.value.length === 0) return null

  const findNode = (nodes: any[], key: string): any => {
    for (const node of nodes) {
      if (node.key === key) return node
      if (node.children) {
        const found = findNode(node.children, key)
        if (found) return found
      }
    }
    return null
  }

  return findNode(treeData.value, selectedKeys.value[0])
})

// 过滤后的数据点 - 排除tombstone=1的数据点
const filteredPoints = computed(() => {
  if (!selectedNode.value || !selectedNode.value.points) return []
  return selectedNode.value.points.filter((point: any) => point.tombstone !== 1)
})

// 获取节点图标
const getNodeIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    device: DatabaseOutlined,
    group: GroupOutlined,
    modbus: ApiOutlined,
    serial: UsbOutlined,
    canbus: NodeIndexOutlined,
    user: UserOutlined
  }
  return iconMap[type] || DatabaseOutlined
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    online: 'green',
    offline: 'red',
    error: 'orange'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    online: '在线',
    offline: '离线',
    error: '故障'
  }
  return textMap[status] || status
}

// 获取类型文本
const getTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    device: '设备',
    group: '分组',
    modbus: 'Modbus',
    serial: '串口设备',
    canbus: 'CAN总线',
    user: '用户'
  }
  return textMap[type] || type
}

// 格式化时间
const formatTime = (time: Date | string | undefined | null) => {
  if (!time) {
    return '-'
  }

  try {
    const date = time instanceof Date ? time : new Date(time)
    if (isNaN(date.getTime())) {
      return '-'
    }
    return format(date, 'yyyy-MM-dd HH:mm:ss')
  } catch (error) {
    console.warn('Invalid date format:', time, error)
    return '-'
  }
}

// 检查节点是否是另一个节点的后代
const isDescendant = (potentialDescendant: Node, ancestor: Node): boolean => {
  const allNodes = nodeStore.nodes.data || []

  const findParent = (nodeId: string): Node | null => {
    return allNodes.find(n => n.id === nodeId) || null
  }

  let current = findParent(potentialDescendant.parent)
  while (current) {
    if (current.id === ancestor.id) {
      return true
    }
    current = findParent(current.parent)
  }

  return false
}

// 展开全部
const handleExpandAll = () => {
  const getAllKeys = (nodes: any[]): string[] => {
    let keys: string[] = []
    nodes.forEach(node => {
      keys.push(node.key)
      if (node.children) {
        keys = keys.concat(getAllKeys(node.children))
      }
    })
    return keys
  }
  expandedKeys.value = getAllKeys(treeData.value)
}

// 收起全部
const handleCollapseAll = () => {
  expandedKeys.value = []
}

// 搜索处理
const handleSearch = () => {
  // 实现搜索逻辑
  message.info(`搜索: ${searchValue.value}`)
}

// 添加设备
const handleAddDevice = () => {
  defaultParentForAdd.value = undefined  // 不预设父节点
  addDeviceModalVisible.value = true
}

// 添加子设备
const handleAddChildDevice = () => {
  // 预设父节点为当前选中的节点
  defaultParentForAdd.value = selectedNode.value?.raw?.id
  addDeviceModalVisible.value = true
}

// 添加设备成功
const handleAddDeviceSuccess = () => {
  // 重新加载设备树数据
  nodeStore.fetchNodes()
  message.success('设备添加成功')
}

// 编辑设备
const handleEditDevice = (node: Node) => {
  editingNode.value = node
  editDeviceModalVisible.value = true
}

// 编辑设备成功
const handleEditDeviceSuccess = () => {
  // 重新加载设备树数据
  nodeStore.fetchNodes()
  message.success('设备编辑成功')
}

// 复制节点
const handleCopyNode = (node: Node) => {
  const nodeDesc = getNodeDescription(node)

  // 显示确认对话框
  Modal.confirm({
    title: '确认复制',
    content: `确定要复制节点 "${nodeDesc}" 吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await nodeStore.copyNode(node.id, node.parent)
        message.success(`节点 "${nodeDesc}" 复制成功`)
        nodeStore.fetchNodes()
      } catch (error) {
        console.error('复制节点失败:', error)
        message.error('复制节点失败')
      }
    }
  })
}

// 移动节点
const handleMoveNode = (node: Node) => {
  const nodeDesc = getNodeDescription(node)

  // 简化实现：显示提示信息，后续可以扩展为完整的移动UI
  Modal.info({
    title: '移动节点',
    content: `移动节点 "${nodeDesc}" 功能正在开发中。\n\n您可以通过拖拽操作来移动节点，或者使用编辑功能修改父节点。`,
    okText: '知道了'
  })
}

// 删除节点
const handleDeleteNode = (node: Node) => {
  const nodeDesc = getNodeDescription(node)

  // 显示确认对话框
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除节点 "${nodeDesc}" 吗？此操作不可撤销。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await nodeStore.deleteNode(node.id, node.parent)
        message.success(`节点 "${nodeDesc}" 删除成功`)
        nodeStore.fetchNodes()
      } catch (error) {
        console.error('删除节点失败:', error)
        message.error('删除节点失败')
      }
    }
  })
}

// 节点选择
const handleNodeSelect = (keys: string[]) => {
  selectedKeys.value = keys
}

// 右键点击
const handleRightClick = ({ node }: any) => {
  rightClickNode.value = node
  contextMenuVisible.value = true
}

// 编辑节点
const handleEdit = () => {
  if (!selectedNode.value?.raw) return
  handleEditDevice(selectedNode.value.raw)
}



// 菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  if (!selectedNode.value?.raw) return

  switch (key) {
    case 'copy':
      handleCopyNode(selectedNode.value.raw)
      break
    case 'move':
      handleMoveNode(selectedNode.value.raw)
      break
    case 'delete':
      handleDeleteNode(selectedNode.value.raw)
      break
  }
}

// 右键菜单点击
const handleContextMenuClick = ({ key }: { key: string }) => {
  contextMenuVisible.value = false
  if (!rightClickNode.value) return

  // 获取原始节点数据
  const nodeData = rightClickNode.value.raw || rightClickNode.value

  switch (key) {
    case 'add':
      handleAddDevice()
      break
    case 'edit':
      handleEditDevice(nodeData)
      break
    case 'copy':
      handleCopyNode(nodeData)
      break
    case 'move':
      handleMoveNode(nodeData)
      break
    case 'delete':
      handleDeleteNode(nodeData)
      break
  }
}

// 初始化
onMounted(async () => {
  try {
    await nodeStore.fetchNodes()
    // 自动展开根节点
    if (treeData.value.length > 0) {
      expandedKeys.value = treeData.value.map(node => node.key)
    }
  } catch (err) {
    console.error('加载设备树数据失败:', err)
    message.error('加载设备树数据失败')
  }
})
</script>

<style scoped lang="less">
.device-tree-page {
  padding: 24px;
  
  .tree-card, .detail-card {
    height: calc(100vh - 200px);
    
    .tree-search {
      margin-bottom: 16px;
    }
    
    .device-tree {
      height: calc(100% - 100px);
      overflow-y: auto;
      
      .tree-node-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        
        .ant-tag {
          margin-left: 8px;
        }
      }
    }
    
    .children-section, .points-section {
      margin-top: 24px;
    }
  }
  
  .detail-card {
    overflow-y: auto;
  }
}

.context-menu {
  position: fixed;
  z-index: 1000;
}
</style>

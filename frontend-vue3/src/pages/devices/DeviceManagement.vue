<template>
  <div class="device-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2 class="page-title">设备管理</h2>
          <p class="page-description">管理和监控您的IoT设备</p>
        </div>
        <div class="header-right">
          <a-space>
            <a-button type="primary" @click="handleAddDevice">
              <template #icon>
                <PlusOutlined />
              </template>
              添加设备
            </a-button>
            <a-button @click="handleRefresh" :loading="loading">
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <DeviceSearch
        :total-count="totalCount"
        :online-count="onlineCount"
        :offline-count="offlineCount"
        :error-count="errorCount"
        @search="handleSearch"
        @view-mode-change="handleViewModeChange"
      />
    </div>

    <!-- 设备网格区域 -->
    <div class="devices-section">
      <DeviceGrid
        :devices="filteredDevices"
        :group-mode="viewMode === 'card'"
        :selected-devices="selectedDevices"
        :loading="loading"
        :has-more="hasMore"
        @device-click="handleDeviceClick"
        @device-edit="handleDeviceEdit"
        @device-configure="handleDeviceConfigure"
        @device-copy="handleDeviceCopy"
        @device-move="handleDeviceMove"
        @device-delete="handleDeviceDelete"
        @group-action="handleGroupAction"
        @add-device="handleAddDevice"
        @load-more="handleLoadMore"
      />
    </div>

    <!-- 设备详情侧边栏 -->
    <a-drawer
      v-model:open="detailDrawerVisible"
      title="设备详情"
      placement="right"
      :width="480"
      :closable="true"
    >
      <div v-if="selectedDevice" class="device-detail-content">
        <h3>{{ getDeviceDescription(selectedDevice) }}</h3>
        <p>设备详情面板开发中...</p>
        <a-space>
          <a-button type="primary" @click="handleDeviceEdit(selectedDevice)">
            编辑设备
          </a-button>
          <a-button @click="handleDeviceConfigure(selectedDevice)">
            设备配置
          </a-button>
        </a-space>
      </div>
    </a-drawer>

    <!-- 添加设备模态框 -->
    <AddDeviceModal
      v-model:open="addDeviceModalVisible"
      :default-parent="defaultParentForAdd"
      @success="handleAddDeviceSuccess"
    />

    <!-- 编辑设备模态框 -->
    <EditDeviceModal
      v-model:open="editDeviceModalVisible"
      :node="editingDevice"
      @success="handleEditDeviceSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import { useNodeStore } from '@/stores/node'
import type { Node } from '@/types/node'
import DeviceSearch from '@/components/devices/DeviceSearch.vue'
import DeviceGrid from '@/components/devices/DeviceGrid.vue'
// import DeviceDetailPanel from '@/components/devices/DeviceDetailPanel.vue'
import AddDeviceModal from '@/components/devices/AddDeviceModal.vue'
import EditDeviceModal from '@/components/devices/EditDeviceModal.vue'

// 搜索筛选接口
interface SearchFilters {
  keyword: string
  type: string
  status: string
  parent: string
  dateRange: any
  hasChildren: string
  viewMode: 'card' | 'list'
}

// 使用节点store
const nodeStore = useNodeStore()

// 页面状态
const loading = ref(false)
const viewMode = ref<'card' | 'list'>('card')
const selectedDevices = ref<string[]>([])
const selectedDevice = ref<Node | null>(null)
const searchFilters = ref<SearchFilters>({
  keyword: '',
  type: '',
  status: '',
  parent: '',
  dateRange: null,
  hasChildren: '',
  viewMode: 'card'
})

// 模态框和抽屉状态
const detailDrawerVisible = ref(false)
const addDeviceModalVisible = ref(false)
const editDeviceModalVisible = ref(false)
const editingDevice = ref<Node | null>(null)
const defaultParentForAdd = ref<string | undefined>(undefined)

// 分页状态
const hasMore = ref(false)

// 获取设备描述
const getDeviceDescription = (device: Node): string => {
  const descPoint = device.points.find(p => p.type === 'description')
  return descPoint?.text || descPoint?.value?.toString() || device.id
}

// 获取设备状态
const getDeviceStatus = (device: Node): string => {
  const statePoint = device.points.find(p => p.type === 'sysState')
  if (statePoint) {
    switch (statePoint.text || statePoint.value) {
      case 'online':
        return 'online'
      case 'offline':
      case 'powerOff':
        return 'offline'
      default:
        return 'error'
    }
  }
  return 'offline'
}

// 过滤设备列表
const filteredDevices = computed(() => {
  let devices = nodeStore.nodes.data || []
  
  // 过滤已删除的设备
  devices = devices.filter(device => device.tombstone !== 1)
  
  // 关键词搜索
  if (searchFilters.value.keyword) {
    const keyword = searchFilters.value.keyword.toLowerCase()
    devices = devices.filter(device => {
      const name = getDeviceDescription(device).toLowerCase()
      return name.includes(keyword) || device.id.toLowerCase().includes(keyword)
    })
  }
  
  // 设备类型筛选
  if (searchFilters.value.type) {
    devices = devices.filter(device => device.type === searchFilters.value.type)
  }
  
  // 设备状态筛选
  if (searchFilters.value.status) {
    devices = devices.filter(device => 
      getDeviceStatus(device) === searchFilters.value.status
    )
  }
  
  // 父节点筛选
  if (searchFilters.value.parent) {
    devices = devices.filter(device => device.parent === searchFilters.value.parent)
  }
  
  // 子节点筛选
  if (searchFilters.value.hasChildren) {
    const hasChildrenFilter = searchFilters.value.hasChildren === 'true'
    devices = devices.filter(device => {
      const childrenCount = devices.filter(d => d.parent === device.id).length
      return hasChildrenFilter ? childrenCount > 0 : childrenCount === 0
    })
  }
  
  return devices
})

// 统计信息
const totalCount = computed(() => filteredDevices.value.length)
const onlineCount = computed(() => 
  filteredDevices.value.filter(device => getDeviceStatus(device) === 'online').length
)
const offlineCount = computed(() => 
  filteredDevices.value.filter(device => getDeviceStatus(device) === 'offline').length
)
const errorCount = computed(() => 
  filteredDevices.value.filter(device => getDeviceStatus(device) === 'error').length
)

// 处理搜索
const handleSearch = (filters: SearchFilters) => {
  searchFilters.value = { ...filters }
}

// 处理视图模式切换
const handleViewModeChange = (mode: 'card' | 'list') => {
  viewMode.value = mode
}

// 处理设备点击
const handleDeviceClick = (device: Node) => {
  selectedDevice.value = device
  selectedDevices.value = [device.id]
  detailDrawerVisible.value = true
}

// 处理设备编辑
const handleDeviceEdit = (device: Node) => {
  editingDevice.value = device
  editDeviceModalVisible.value = true
}

// 处理设备配置
const handleDeviceConfigure = (device: Node) => {
  // 跳转到设备配置页面或打开配置模态框
  message.info(`配置设备: ${getDeviceDescription(device)}`)
}

// 处理设备复制
const handleDeviceCopy = (device: Node) => {
  const deviceDesc = getDeviceDescription(device)
  
  Modal.confirm({
    title: '确认复制',
    content: `确定要复制设备 "${deviceDesc}" 吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await nodeStore.copyNode(device.id, device.parent)
        message.success(`设备 "${deviceDesc}" 复制成功`)
        handleRefresh()
      } catch (error) {
        console.error('复制设备失败:', error)
        message.error('复制设备失败')
      }
    }
  })
}

// 处理设备移动
const handleDeviceMove = (device: Node) => {
  const deviceDesc = getDeviceDescription(device)
  Modal.info({
    title: '移动设备',
    content: `移动设备 "${deviceDesc}" 功能正在开发中。`,
    okText: '知道了'
  })
}

// 处理设备删除
const handleDeviceDelete = (device: Node) => {
  const deviceDesc = getDeviceDescription(device)
  
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除设备 "${deviceDesc}" 吗？此操作不可撤销。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await nodeStore.deleteNode(device.id, device.parent)
        message.success(`设备 "${deviceDesc}" 删除成功`)
        handleRefresh()
      } catch (error) {
        console.error('删除设备失败:', error)
        message.error('删除设备失败')
      }
    }
  })
}

// 处理分组操作
const handleGroupAction = (group: any, action: string) => {
  message.info(`分组操作: ${action} - ${group.name}`)
}

// 处理添加设备
const handleAddDevice = () => {
  defaultParentForAdd.value = undefined
  addDeviceModalVisible.value = true
}

// 处理添加设备成功
const handleAddDeviceSuccess = () => {
  handleRefresh()
  message.success('设备添加成功')
}

// 处理编辑设备成功
const handleEditDeviceSuccess = () => {
  handleRefresh()
  message.success('设备编辑成功')
}

// 处理加载更多
const handleLoadMore = () => {
  // 实现分页加载逻辑
  message.info('加载更多设备')
}

// 刷新设备列表
const handleRefresh = async () => {
  loading.value = true
  try {
    await nodeStore.fetchNodes()
  } catch (error) {
    console.error('刷新设备列表失败:', error)
    message.error('刷新设备列表失败')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(async () => {
  await handleRefresh()
})
</script>

<style scoped lang="less">
@import '@/styles/variables.less';

.device-management-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: calc(100vh - @layout-header-height);

  .page-header {
    background-color: white;
    border-radius: @border-radius-base;
    padding: 24px;
    margin-bottom: 16px;
    box-shadow: @box-shadow-base;

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-left {
        .page-title {
          margin: 0;
          font-size: 24px;
          font-weight: 600;
          color: #262626;
        }

        .page-description {
          margin: 4px 0 0 0;
          color: #8c8c8c;
          font-size: 14px;
        }
      }
    }
  }

  .search-section {
    background-color: white;
    border-radius: @border-radius-base;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: @box-shadow-base;
  }

  .devices-section {
    background-color: white;
    border-radius: @border-radius-base;
    padding: 24px;
    box-shadow: @box-shadow-base;
    min-height: 400px;
  }

  // 响应式布局
  @media (max-width: 768px) {
    padding: 16px;

    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .search-section,
    .devices-section {
      padding: 16px;
    }
  }
}
</style>

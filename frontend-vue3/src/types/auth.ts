// 认证相关类型定义

export interface User {
  token: string
  email: string
}

export interface LoginRequest {
  email: string  // 实际上可以是用户名或邮箱
  password: string
}

export interface LoginResponse {
  token: string
  email: string
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

// Zod 验证模式
import { z } from 'zod'

export const LoginRequestSchema = z.object({
  email: z.string().min(1, '请输入用户名或邮箱'),
  password: z.string().min(1, '密码不能为空')
})

export const UserSchema = z.object({
  token: z.string(),
  email: z.string()  // 可以是用户名或邮箱
})

export type LoginRequestType = z.infer<typeof LoginRequestSchema>
export type UserType = z.infer<typeof UserSchema>

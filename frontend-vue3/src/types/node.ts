// 节点相关类型定义
import { z } from 'zod'
import { Point, NodeType as NodeTypeEnum } from './common'

// 后端NodeEdge结构对应的接口
export interface Node {
  id: string
  type: string  // 后端使用string类型，不是enum
  hash: number
  parent: string
  points: Point[]
  edgePoints: Point[]
}

export interface NodeView extends Node {
  children: NodeView[]
  expanded: boolean
  level: number
}

export interface NodeEdit {
  id?: string
  type: NodeTypeEnum
  parent: string
  points: Point[]
  edgePoints: Point[]
}

export interface CreateNodeRequest {
  type: string  // 后端使用string类型
  parent: string
  points?: Point[]
  edgePoints?: Point[]
}

// 用于发送的CreateNodeRequest接口（对应后端NodeEdge结构）
export interface CreateNodeSendRequest {
  id?: string  // 可选，后端会自动生成
  type: string
  hash?: number  // 可选，后端会处理
  parent: string
  points?: {
    type: string
    key: string
    value?: number
    time: string  // 字符串格式的时间
    text?: string
    data?: Uint8Array
    tombstone?: number
    origin?: string
  }[]
  edgePoints?: {
    type: string
    key: string
    value?: number
    time: string  // 字符串格式的时间
    text?: string
    data?: Uint8Array
    tombstone?: number
    origin?: string
  }[]
}

export interface UpdateNodeRequest {
  type?: string  // 后端使用string类型
  parent?: string
  oldParent?: string  // 移动节点时需要的原父节点ID
  points?: Point[]
  edgePoints?: Point[]
}

// 用于发送的UpdateNodeRequest接口
export interface UpdateNodeSendRequest {
  type?: string
  parent?: string
  oldParent?: string  // 移动节点时需要的原父节点ID
  points?: {
    type: string
    key: string
    value?: number
    time: string  // 字符串格式的时间
    text?: string
    data?: Uint8Array
    tombstone?: number
    origin?: string
  }[]
  edgePoints?: {
    type: string
    key: string
    value?: number
    time: string  // 字符串格式的时间
    text?: string
    data?: Uint8Array
    tombstone?: number
    origin?: string
  }[]
}

// Zod 验证模式
export const PointSchema = z.object({
  type: z.string(),
  key: z.string(),
  value: z.number().optional(),  // 可能为空
  time: z.string().transform(str => new Date(str)),  // 后端返回字符串，转换为Date
  text: z.string().optional(),
  data: z.instanceof(Uint8Array).optional(),
  tombstone: z.number().optional(),
  origin: z.string().optional()
})

// 用于发送到后端的Point schema（时间保持为字符串）
export const PointSendSchema = z.object({
  type: z.string(),
  key: z.string(),
  value: z.number().optional(),
  time: z.string(),  // 发送时保持字符串格式
  text: z.string().optional(),
  data: z.instanceof(Uint8Array).optional(),
  tombstone: z.number().optional(),
  origin: z.string().optional()
})

export const NodeSchema = z.object({
  id: z.string(),
  type: z.string(),  // 后端使用string类型
  hash: z.number(),
  parent: z.string(),
  points: z.array(PointSchema),
  edgePoints: z.array(PointSchema)
})

export const CreateNodeRequestSchema = z.object({
  id: z.string().optional(),  // 可选，后端会自动生成
  type: z.string(),  // 后端使用string类型
  hash: z.number().optional(),  // 可选，后端会处理
  parent: z.string(),
  points: z.array(PointSendSchema).optional(),
  edgePoints: z.array(PointSendSchema).optional()
})

export type NodeSchemaType = z.infer<typeof NodeSchema>
export type CreateNodeRequestType = z.infer<typeof CreateNodeRequestSchema>

// 通用类型定义

export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
}

export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

export interface SortParams {
  field: string
  order: 'asc' | 'desc'
}

export interface FilterParams {
  [key: string]: any
}

export interface RequestParams {
  pagination?: PaginationParams
  sort?: SortParams
  filters?: FilterParams
}

// 数据状态枚举
export enum DataStatus {
  NotAsked = 'not_asked',
  Loading = 'loading',
  Success = 'success',
  Failure = 'failure'
}

// 数据包装器类型
export interface DataWrapper<T> {
  status: DataStatus
  data?: T
  error?: string
}

// 创建数据包装器的工具函数
export const createDataWrapper = <T>(
  status: DataStatus = DataStatus.NotAsked,
  data?: T,
  error?: string
): DataWrapper<T> => ({
  status,
  data,
  error
})

// 节点类型枚举
export enum NodeType {
  Device = 'device',
  Modbus = 'modbus',
  Variable = 'variable',
  Group = 'group',
  Action = 'action',
  ModbusIO = 'modbus-io',
  OneWire = 'onewire',
  Serial = 'serial',
  Condition = 'condition',
  Rule = 'rule',
  Shelly = 'shelly',
  ShellyIO = 'shelly-io',
  Particle = 'particle',
  Sync = 'sync',
  NTP = 'ntp',
  CanBus = 'canbus',
  SignalGenerator = 'signal-generator',
  NetworkManager = 'network-manager',
  File = 'file',
  Metrics = 'metrics',
  Db = 'db',
  Update = 'update',
  User = 'user'
}

// 数据点类型 - 对应后端Point结构
export interface Point {
  type: string
  key: string
  value?: number  // 后端使用float64，但可能为空
  time: Date
  text?: string
  data?: Uint8Array  // 对应后端的[]byte
  tombstone?: number  // 删除标记
  origin?: string     // 来源
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
}

// 认证状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { AuthAPI } from '@/api/auth'
import { StorageUtil } from '@/utils/storage'
import type { User, LoginRequest } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const isAuthenticated = computed(() => !!user.value?.token)
  const token = computed(() => user.value?.token || null)

  // 初始化认证状态
  const initAuth = () => {
    const savedUser = StorageUtil.getUserInfo()
    const savedToken = StorageUtil.getAuthToken()
    
    if (savedUser && savedToken) {
      user.value = { ...savedUser, token: savedToken }
    }
  }

  // 登录
  const login = async (credentials: LoginRequest): Promise<void> => {
    try {
      isLoading.value = true
      error.value = null

      const loginResponse = await AuthAPI.login(credentials)

      // 构建用户对象
      const userData = {
        email: loginResponse.email,
        token: loginResponse.token
      }

      // 保存用户信息和 token
      user.value = userData
      StorageUtil.setAuthToken(userData.token)
      StorageUtil.setUserInfo(userData)
      
    } catch (err) {
      error.value = err instanceof Error ? err.message : '登录失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    try {
      await AuthAPI.logout()
    } catch (err) {
      console.warn('Logout API call failed:', err)
    } finally {
      // 清除本地状态和存储
      user.value = null
      error.value = null
      StorageUtil.clearAuth()
    }
  }

  // 验证 token
  const validateToken = async (): Promise<boolean> => {
    if (!token.value) return false

    try {
      const isValid = await AuthAPI.validateToken(token.value)
      if (!isValid) {
        await logout()
      }
      return isValid
    } catch {
      await logout()
      return false
    }
  }

  // 刷新 token - Simple IoT不支持token刷新
  const refreshToken = async (): Promise<void> => {
    // Simple IoT的JWT token有效期很长，不支持刷新
    // 如果token过期，直接登出让用户重新登录
    await logout()
    throw new Error('Token expired, please login again')
  }

  // 清除错误
  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    user,
    isLoading,
    error,
    
    // 计算属性
    isAuthenticated,
    token,
    
    // 方法
    initAuth,
    login,
    logout,
    validateToken,
    refreshToken,
    clearError
  }
})

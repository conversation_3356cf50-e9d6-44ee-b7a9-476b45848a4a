// 应用状态管理
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { StorageUtil } from '@/utils/storage'

export const useAppStore = defineStore('app', () => {
  // 状态
  const theme = ref<string>('light')
  const language = ref<string>('zh-CN')
  const sidebarCollapsed = ref<boolean>(false)
  const loading = ref<boolean>(false)

  // 初始化应用状态
  const initApp = () => {
    // 从本地存储恢复主题设置
    const savedTheme = StorageUtil.getTheme()
    if (savedTheme) {
      theme.value = savedTheme
      document.documentElement.setAttribute('data-theme', savedTheme)
    }

    // 从本地存储恢复语言设置
    const savedLanguage = StorageUtil.getLanguage()
    if (savedLanguage) {
      language.value = savedLanguage
    }

    // 从本地存储恢复侧边栏状态
    sidebarCollapsed.value = StorageUtil.getSidebarCollapsed()
  }

  // 设置主题
  const setTheme = (newTheme: string) => {
    theme.value = newTheme
    StorageUtil.setTheme(newTheme)

    // 应用主题到 document
    document.documentElement.setAttribute('data-theme', newTheme)
  }

  // 设置侧边栏折叠状态
  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
    StorageUtil.setSidebarCollapsed(collapsed)
  }

  // 设置语言
  const setLanguage = (newLanguage: string) => {
    language.value = newLanguage
    StorageUtil.setLanguage(newLanguage)
  }

  // 设置加载状态
  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    StorageUtil.setSidebarCollapsed(sidebarCollapsed.value)
  }

  return {
    // 状态
    theme,
    language,
    sidebarCollapsed,
    loading,

    // 方法
    initApp,
    setTheme,
    setLanguage,
    setSidebarCollapsed,
    setLoading,
    toggleSidebar
  }
})

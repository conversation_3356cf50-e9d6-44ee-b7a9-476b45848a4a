// 节点/设备状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { nodeApi } from '@/api/node'
import type { Node, NodeView, CreateNodeRequest, CreateNodeSendRequest, UpdateNodeRequest, UpdateNodeSendRequest } from '@/types/node'
import type { DataWrapper, RequestParams } from '@/types/common'
import { DataStatus, createDataWrapper } from '@/types/common'

export const useNodeStore = defineStore('node', () => {
  // 状态
  const nodes = ref<DataWrapper<Node[]>>(createDataWrapper())
  const selectedNode = ref<Node | null>(null)
  const nodeTree = ref<DataWrapper<NodeView[]>>(createDataWrapper())
  const searchResults = ref<DataWrapper<Node[]>>(createDataWrapper())
  
  // 操作状态
  const isCreating = ref(false)
  const isUpdating = ref(false)
  const isDeleting = ref(false)
  const isMoving = ref(false)
  const isCopying = ref(false)

  // 计算属性
  const isLoading = computed(() => 
    nodes.value.status === DataStatus.Loading ||
    nodeTree.value.status === DataStatus.Loading ||
    searchResults.value.status === DataStatus.Loading
  )

  const hasError = computed(() => 
    nodes.value.status === DataStatus.Failure ||
    nodeTree.value.status === DataStatus.Failure ||
    searchResults.value.status === DataStatus.Failure
  )

  const errorMessage = computed(() => 
    nodes.value.error || 
    nodeTree.value.error || 
    searchResults.value.error
  )

  // 获取所有节点
  const fetchNodes = async (params?: RequestParams) => {
    nodes.value = createDataWrapper(DataStatus.Loading)
    
    try {
      const data = await nodeApi.getNodes(params)
      nodes.value = createDataWrapper(DataStatus.Success, data)
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '获取节点列表失败'
      nodes.value = createDataWrapper(DataStatus.Failure, undefined, errorMsg)
      throw error
    }
  }

  // 获取节点树
  const fetchNodeTree = async (rootId?: string) => {
    nodeTree.value = createDataWrapper(DataStatus.Loading)
    
    try {
      const data = await nodeApi.getNodeTree(rootId)
      const treeData = buildNodeTree(data)
      nodeTree.value = createDataWrapper(DataStatus.Success, treeData)
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '获取节点树失败'
      nodeTree.value = createDataWrapper(DataStatus.Failure, undefined, errorMsg)
      throw error
    }
  }

  // 搜索节点
  const searchNodes = async (query: string, filters?: Record<string, any>) => {
    searchResults.value = createDataWrapper(DataStatus.Loading)
    
    try {
      const data = await nodeApi.searchNodes(query, filters)
      searchResults.value = createDataWrapper(DataStatus.Success, data)
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '搜索节点失败'
      searchResults.value = createDataWrapper(DataStatus.Failure, undefined, errorMsg)
      throw error
    }
  }

  // 创建节点
  const createNode = async (data: CreateNodeSendRequest) => {
    isCreating.value = true

    try {
      const response = await nodeApi.createNode(data)

      // 创建成功后重新获取节点列表
      if (response.success) {
        await fetchNodes()
      }

      return response
    } catch (error) {
      throw error
    } finally {
      isCreating.value = false
    }
  }

  // 更新节点
  const updateNode = async (id: string, data: UpdateNodeSendRequest) => {
    isUpdating.value = true

    try {
      await nodeApi.updateNode(id, data)

      // 重新获取所有节点列表来更新状态（与原有Elm前端行为一致）
      await fetchNodes()
    } catch (error) {
      throw error
    } finally {
      isUpdating.value = false
    }
  }

  // 删除节点
  const deleteNode = async (id: string, parent?: string) => {
    isDeleting.value = true

    try {
      await nodeApi.deleteNode(id, parent)

      // 更新本地状态
      if (nodes.value.data) {
        nodes.value.data = nodes.value.data.filter(node => node.id !== id)
      }

      // 清除选中状态
      if (selectedNode.value?.id === id) {
        selectedNode.value = null
      }
    } catch (error) {
      throw error
    } finally {
      isDeleting.value = false
    }
  }

  // 移动节点
  const moveNode = async (nodeId: string, oldParentId: string, newParentId: string) => {
    isMoving.value = true

    try {
      await nodeApi.moveNode(nodeId, oldParentId, newParentId)

      // 重新获取节点列表以更新本地状态
      await fetchNodes()
    } catch (error) {
      throw error
    } finally {
      isMoving.value = false
    }
  }

  // 复制节点
  const copyNode = async (nodeId: string, targetParentId: string) => {
    isCopying.value = true

    try {
      await nodeApi.copyNode(nodeId, targetParentId)

      // 重新获取节点列表以更新本地状态
      await fetchNodes()
    } catch (error) {
      throw error
    } finally {
      isCopying.value = false
    }
  }

  // 选择节点
  const selectNode = (node: Node | null) => {
    selectedNode.value = node
  }

  // 构建节点树的辅助函数
  const buildNodeTree = (nodes: Node[]): NodeView[] => {
    const nodeMap = new Map<string, NodeView>()
    const rootNodes: NodeView[] = []

    // 创建节点映射
    nodes.forEach(node => {
      nodeMap.set(node.id, {
        ...node,
        children: [],
        expanded: false,
        level: 0
      })
    })

    // 构建树形结构
    nodes.forEach(node => {
      const nodeView = nodeMap.get(node.id)!
      
      if (node.parent && nodeMap.has(node.parent)) {
        const parent = nodeMap.get(node.parent)!
        parent.children.push(nodeView)
        nodeView.level = parent.level + 1
      } else {
        rootNodes.push(nodeView)
      }
    })

    return rootNodes
  }

  // 重置状态
  const reset = () => {
    nodes.value = createDataWrapper()
    selectedNode.value = null
    nodeTree.value = createDataWrapper()
    searchResults.value = createDataWrapper()
    isCreating.value = false
    isUpdating.value = false
    isDeleting.value = false
    isMoving.value = false
    isCopying.value = false
  }

  return {
    // 状态
    nodes,
    selectedNode,
    nodeTree,
    searchResults,
    isCreating,
    isUpdating,
    isDeleting,
    isMoving,
    isCopying,
    
    // 计算属性
    isLoading,
    hasError,
    errorMessage,
    
    // 方法
    fetchNodes,
    fetchNodeTree,
    searchNodes,
    createNode,
    updateNode,
    deleteNode,
    moveNode,
    copyNode,
    selectNode,
    reset
  }
})

{"name": "simpleiot-frontend-vue3", "version": "1.0.0", "description": "Simple IoT Frontend Vue3 Migration", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "ant-design-vue": "^4.0.7", "@ant-design/icons-vue": "^7.0.1", "axios": "^1.6.2", "@tanstack/vue-query": "^5.8.4", "zod": "^3.22.4", "date-fns": "^2.30.0", "lodash-es": "^4.17.21"}, "devDependencies": {"@types/node": "^20.9.0", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.2", "@vue/tsconfig": "^0.4.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "jsdom": "^23.0.1", "less": "^4.2.0", "prettier": "^3.1.0", "typescript": "~5.2.0", "vite": "^5.0.0", "vitest": "^0.34.6", "vue-tsc": "^1.8.27"}, "keywords": ["vue3", "typescript", "ant-design-vue", "iot", "frontend"], "author": "Simple IoT Team", "license": "Apache-2.0"}
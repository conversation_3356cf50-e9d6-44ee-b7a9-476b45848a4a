# Simple IoT Frontend Vue3

这是 Simple IoT 项目的 Vue3 前端实现，用于替换现有的 Elm 前端。

## 技术栈

- **框架**: Vue 3.3+ (Composition API)
- **语言**: TypeScript 5.0+ (严格模式)
- **构建工具**: Vite 4.0+
- **UI库**: Ant Design Vue 4.0+
- **状态管理**: Pinia 2.0+
- **路由**: Vue Router 4.0+
- **HTTP客户端**: Axios + @tanstack/vue-query
- **样式**: Less + CSS Modules
- **测试**: Vitest + Vue Test Utils

## 项目结构

```
frontend-vue3/
├── src/
│   ├── api/              # API接口层
│   ├── components/       # 组件库
│   │   ├── common/       # 基础组件
│   │   ├── node/         # 设备组件
│   │   └── ui/           # UI组件
│   ├── composables/      # 组合式API
│   ├── pages/            # 页面组件
│   ├── router/           # 路由配置
│   ├── stores/           # Pinia状态管理
│   ├── types/            # TypeScript类型
│   ├── utils/            # 工具函数
│   └── styles/           # 样式文件
├── public/               # 静态资源
├── tests/                # 测试文件
└── package.json          # 依赖配置
```

## 开发指南

### 安装依赖

```bash
npm install
# 或
pnpm install
```

### 开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 运行测试

```bash
npm run test
```

### 代码检查

```bash
npm run lint
```

### 代码格式化

```bash
npm run format
```

## 开发状态

### Phase 1: 基础架构和认证模块 ✅

- [x] 项目脚手架搭建
- [x] 认证系统迁移
- [x] 基础UI组件库
- [x] 路由系统配置
- [ ] 第一阶段测试

### Phase 2: 核心设备管理功能 (待开发)

- [ ] 设备列表展示
- [ ] 设备树形结构
- [ ] 基础设备操作
- [ ] 高频设备类型组件

### Phase 3: 中频设备类型 (待开发)

- [ ] 中频设备类型组件开发
- [ ] 设备配置界面优化
- [ ] 数据点管理功能

### Phase 4: 剩余设备类型和高级功能 (待开发)

- [ ] 剩余设备类型组件开发
- [ ] 高级功能和性能优化
- [ ] 系统集成测试

## 环境变量

创建 `.env.local` 文件配置环境变量：

```env
VITE_API_URL=http://localhost:8080
VITE_WS_URL=ws://localhost:8080
VITE_APP_TITLE=Simple IoT
```

## 贡献指南

1. 遵循 TypeScript 严格模式
2. 使用 Composition API
3. 遵循 Ant Design Vue 设计规范
4. 编写单元测试
5. 遵循 ESLint 和 Prettier 规则

## 许可证

Apache-2.0

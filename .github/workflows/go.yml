name: Go
on: [push]
jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Set up Go 1.22
        uses: actions/setup-go@v4
        with:
          go-version: 1.22.0
        id: go

      - name: Set up nodejs
        uses: actions/setup-node@v1
        with:
          node-version: 16.x
        id: node

      - name: Check out code into the Go module directory
        uses: actions/checkout@v3

      - name: Get dependencies
        run: |
          . envsetup.sh
          siot_install_frontend_deps

      - name: Test/lint frontend
        run: |
          . envsetup.sh
          siot_build_frontend
          siot_test_frontend

      - name: Test Backend
        run: |
          node --version
          . envsetup.sh
          go test -p=1 -race "$@" ./...

      - name: Lint backend
        uses: golangci/golangci-lint-action@v3.4.0
        with:
          args: --timeout=5m

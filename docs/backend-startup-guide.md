# Simple IoT 后端服务启动指南

## 📋 系统要求

### Go 环境
- **Go 版本**: 1.19+ (推荐 1.21+)
- **操作系统**: Linux, macOS, Windows
- **内存**: 最少 512MB RAM
- **存储**: 最少 100MB 可用空间

### 依赖检查
```bash
# 检查 Go 版本
go version

# 检查 Git (用于下载依赖)
git version
```

## 🚀 快速启动

### 1. 构建应用
```bash
# 在项目根目录下
cd /home/<USER>/goProjects/simpleiot

# 构建 Simple IoT 应用
go build -o siot cmd/siot/main.go
```

### 2. 启动服务器
```bash
# 使用默认配置启动
./siot

# 或者显式指定 serve 命令
./siot serve
```

### 3. 验证启动
服务启动后，你应该看到类似的输出：
```
SimpleIOT Development
SIOT started
```

## 🔧 配置选项

### 命令行参数
```bash
# 查看所有可用选项
./siot serve -help

# 常用选项
./siot serve \
  -store="siot.sqlite" \          # 数据库文件路径
  -debugHttp=false \              # HTTP 调试模式
  -dev=false \                    # 开发模式
  -resetStore=false               # 重置数据库
```

### 环境变量配置
```bash
# HTTP 服务端口 (默认: 8118)
export SIOT_HTTP_PORT=8118

# NATS 服务端口 (默认: 4222)
export SIOT_NATS_PORT=4222

# NATS HTTP 管理端口 (默认: 8222)
export SIOT_NATS_HTTP_PORT=8222

# NATS WebSocket 端口 (默认: 9222)
export SIOT_NATS_WS_PORT=9222

# 数据目录
export SIOT_DATA=./

# 认证令牌
export SIOT_AUTH_TOKEN=""

# NATS 服务器地址
export SIOT_NATS_SERVER="nats://127.0.0.1:4222"
```

## 🌐 服务端口说明

| 服务 | 默认端口 | 用途 | 环境变量 |
|------|----------|------|----------|
| HTTP Web UI | 8118 | 前端界面访问 | SIOT_HTTP_PORT |
| NATS 消息服务 | 4222 | 内部消息通信 | SIOT_NATS_PORT |
| NATS HTTP 管理 | 8222 | NATS 管理界面 | SIOT_NATS_HTTP_PORT |
| NATS WebSocket | 9222 | WebSocket 连接 | SIOT_NATS_WS_PORT |

## 📁 数据存储

### 默认数据文件
- **数据库**: `siot.sqlite` (SQLite 数据库)
- **位置**: 当前工作目录或 `SIOT_DATA` 指定目录

### 数据目录结构
```
./
├── siot.sqlite          # 主数据库文件
├── siot                 # 可执行文件
└── assets/              # 静态资源文件 (自动创建)
```

## 🔐 默认用户账号

### 管理员账号
- **用户名**: `admin`
- **密码**: `admin`
- **权限**: 系统管理员

⚠️ **安全提示**: 首次登录后请立即修改默认密码！

## 🌍 访问应用

### Web 界面
```bash
# 默认访问地址
http://localhost:8118

# 如果修改了端口
http://localhost:${SIOT_HTTP_PORT}
```

### API 接口
```bash
# 认证 API
POST http://localhost:8118/v1/auth

# 节点 API
GET http://localhost:8118/v1/nodes
```

## 🛠️ 开发模式

### 启用开发模式
```bash
# 开发模式启动 (启用调试功能)
./siot serve -dev=true -debugHttp=true

# 或使用环境变量
export SIOT_DEV=true
./siot serve
```

### 开发模式特性
- 详细的 HTTP 请求日志
- 更详细的错误信息
- 热重载支持 (需要额外工具)

## 🔄 数据管理

### 重置数据库
```bash
# 启动时重置所有数据 (谨慎使用!)
./siot serve -resetStore=true
```

### 数据备份
```bash
# 导出所有数据
./siot export > backup.yaml

# 导入数据
./siot import -parentID=root < backup.yaml
```

### 数据库维护
```bash
# 检查数据库完整性
./siot store -check

# 修复数据库
./siot store -fix
```

## 📊 监控和日志

### 查看实时日志
```bash
# 查看系统日志
./siot log

# 使用自定义 NATS 服务器
./siot log -natsServer="nats://localhost:4222"
```

### 系统日志
```bash
# 启用系统日志 (Linux)
./siot serve -syslog=true
```

## 🚨 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
netstat -tlnp | grep :8118

# 使用不同端口
export SIOT_HTTP_PORT=8119
./siot serve
```

#### 2. 权限问题
```bash
# 确保有写入权限
chmod 755 ./
chmod 644 siot.sqlite
```

#### 3. 数据库损坏
```bash
# 检查数据库
./siot store -check

# 修复数据库
./siot store -fix

# 最后手段：重置数据库
./siot serve -resetStore=true
```

#### 4. 内存不足
```bash
# 检查内存使用
free -h

# 监控进程内存
top -p $(pgrep siot)
```

### 调试模式
```bash
# 启用所有调试选项
./siot serve \
  -dev=true \
  -debugHttp=true \
  -debugLifecycle=true
```

## 🔧 高级配置

### 自定义 UI 目录
```bash
# 使用自定义前端文件
./siot serve -customUIDir="/path/to/custom/ui"
```

### TLS/SSL 配置
```bash
# 配置 NATS TLS
export SIOT_NATS_TLS_CERT="/path/to/cert.pem"
export SIOT_NATS_TLS_KEY="/path/to/key.pem"
export SIOT_NATS_TLS_TIMEOUT="2.0"
```

### 外部 NATS 服务器
```bash
# 禁用内置 NATS 服务器
./siot serve -natsDisableServer=true -natsServer="nats://external-nats:4222"
```

## 📝 启动脚本示例

### 基础启动脚本
```bash
#!/bin/bash
# start-siot.sh

# 设置环境变量
export SIOT_HTTP_PORT=8118
export SIOT_DATA="./data"

# 创建数据目录
mkdir -p $SIOT_DATA

# 启动服务
cd /home/<USER>/goProjects/simpleiot
./siot serve
```

### 生产环境启动脚本
```bash
#!/bin/bash
# start-siot-prod.sh

# 生产环境配置
export SIOT_HTTP_PORT=8118
export SIOT_DATA="/var/lib/siot"
export SIOT_AUTH_TOKEN="your-secure-token"

# 启动服务 (后台运行)
cd /home/<USER>/goProjects/simpleiot
nohup ./siot serve -syslog=true > /dev/null 2>&1 &

echo "Simple IoT started in background"
echo "PID: $!"
```

## 🎯 下一步

1. **访问 Web 界面**: http://localhost:8118
2. **使用默认账号登录**: admin / admin
3. **修改默认密码**
4. **开始配置设备和传感器**

---

*更新日期: 2024-12-28*  
*适用版本: Simple IoT Development*

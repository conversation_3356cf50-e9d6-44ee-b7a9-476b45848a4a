(kicad_sch (version 20211123) (generator eeschema)

  (uuid 20c4d52b-6ca5-4431-b166-04146a79621b)

  (paper "A")

  (lib_symbols
    (symbol "Connector_Generic:Conn_01x05" (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "J" (id 0) (at 0 7.62 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "Conn_01x05" (id 1) (at 0 -7.62 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "connector" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Generic connector, single row, 01x05, script generated (kicad-library-utils/schlib/autogen/connector/)" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Connector*:*_1x??_*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "Conn_01x05_1_1"
        (rectangle (start -1.27 -4.953) (end 0 -5.207)
          (stroke (width 0.1524) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (rectangle (start -1.27 -2.413) (end 0 -2.667)
          (stroke (width 0.1524) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (rectangle (start -1.27 0.127) (end 0 -0.127)
          (stroke (width 0.1524) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (rectangle (start -1.27 2.667) (end 0 2.413)
          (stroke (width 0.1524) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (rectangle (start -1.27 5.207) (end 0 4.953)
          (stroke (width 0.1524) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (rectangle (start -1.27 6.35) (end 1.27 -6.35)
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type background))
        )
        (pin passive line (at -5.08 5.08 0) (length 3.81)
          (name "Pin_1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -5.08 2.54 0) (length 3.81)
          (name "Pin_2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -5.08 0 0) (length 3.81)
          (name "Pin_3" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -5.08 -2.54 0) (length 3.81)
          (name "Pin_4" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -5.08 -5.08 0) (length 3.81)
          (name "Pin_5" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:R_US" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.54 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R_US" (id 1) (at -2.54 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 1.016 -0.254 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor, US symbol" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_US_0_1"
        (polyline
          (pts
            (xy 0 -2.286)
            (xy 0 -2.54)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 2.286)
            (xy 0 2.54)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 -0.762)
            (xy 1.016 -1.143)
            (xy 0 -1.524)
            (xy -1.016 -1.905)
            (xy 0 -2.286)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 0.762)
            (xy 1.016 0.381)
            (xy 0 0)
            (xy -1.016 -0.381)
            (xy 0 -0.762)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 2.286)
            (xy 1.016 1.905)
            (xy 0 1.524)
            (xy -1.016 1.143)
            (xy 0 0.762)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
      )
      (symbol "R_US_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Sensor_Temperature:DS18B20" (pin_names (offset 1.016)) (in_bom yes) (on_board yes)
      (property "Reference" "U" (id 0) (at -3.81 6.35 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "DS18B20" (id 1) (at 6.35 6.35 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "Package_TO_SOT_THT:TO-92_Inline" (id 2) (at -25.4 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "http://datasheets.maximintegrated.com/en/ds/DS18B20.pdf" (id 3) (at -3.81 6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "OneWire 1Wire Dallas Maxim" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Programmable Resolution 1-Wire Digital Thermometer TO-92" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "TO*92*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "DS18B20_0_1"
        (rectangle (start -5.08 5.08) (end 5.08 -5.08)
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type background))
        )
        (circle (center -3.302 -2.54) (radius 1.27)
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type outline))
        )
        (rectangle (start -2.667 -1.905) (end -3.937 0)
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type outline))
        )
        (arc (start -2.667 3.175) (mid -3.302 3.81) (end -3.937 3.175)
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -3.937 0.635)
            (xy -3.302 0.635)
          )
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -3.937 1.27)
            (xy -3.302 1.27)
          )
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -3.937 1.905)
            (xy -3.302 1.905)
          )
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -3.937 2.54)
            (xy -3.302 2.54)
          )
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -3.937 3.175)
            (xy -3.937 0)
          )
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -3.937 3.175)
            (xy -3.302 3.175)
          )
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.667 3.175)
            (xy -2.667 0)
          )
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type none))
        )
      )
      (symbol "DS18B20_1_1"
        (pin power_in line (at 0 -7.62 90) (length 2.54)
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 7.62 0 180) (length 2.54)
          (name "DQ" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 0 7.62 270) (length 2.54)
          (name "VDD" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 67.31 45.72) (diameter 0) (color 0 0 0 0)
    (uuid 66dcc784-e0b4-4171-b6ec-1825faa8db59)
  )
  (junction (at 67.31 38.1) (diameter 0) (color 0 0 0 0)
    (uuid b3fc600d-87a9-46db-91a2-e72c2873a845)
  )

  (wire (pts (xy 76.2 35.56) (xy 76.2 38.1))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 05b919e3-60ba-431f-b66c-19c317cba73c)
  )
  (wire (pts (xy 71.12 48.26) (xy 63.5 48.26))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 0f692535-9d2f-4266-b058-094565dbb913)
  )
  (wire (pts (xy 63.5 45.72) (xy 67.31 45.72))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 26b4d552-5437-499c-8761-12d98cf16bd9)
  )
  (wire (pts (xy 80.01 43.18) (xy 76.2 43.18))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 37758953-a461-4bc6-8e16-cf59d7fc1c51)
  )
  (wire (pts (xy 71.12 50.8) (xy 71.12 48.26))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 3dc944bf-1e6c-4a3a-b9b4-8a9fdae05a90)
  )
  (wire (pts (xy 76.2 45.72) (xy 67.31 45.72))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 6adee3f0-506e-465e-924c-be78ba709939)
  )
  (wire (pts (xy 87.63 50.8) (xy 71.12 50.8))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 910e7365-2d1a-40c5-8eea-2555f62db17c)
  )
  (wire (pts (xy 63.5 38.1) (xy 67.31 38.1))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid a4dff66e-e1db-4787-9f26-1e91967ce48e)
  )
  (wire (pts (xy 76.2 38.1) (xy 67.31 38.1))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid ad18ed64-b81f-4eaf-bbaa-9e626b9ade70)
  )
  (wire (pts (xy 87.63 35.56) (xy 76.2 35.56))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid b9df02d3-1cc0-4fd7-92ff-dcbc7fa9dc88)
  )
  (wire (pts (xy 76.2 43.18) (xy 76.2 45.72))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid d8e77be2-18d4-430c-997e-0f307a2cdc48)
  )

  (text "3v3" (at 55.88 39.37 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
    (uuid 00f912fa-70a3-4a63-be5d-58f894c47766)
  )
  (text "GPIO 3" (at 55.88 44.45 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
    (uuid 08e03f3b-a61a-436f-81cb-b1d5be5b7185)
  )
  (text "GPIO 4" (at 55.88 46.99 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
    (uuid beee7b68-8d18-4455-8216-6995808a0eb6)
  )
  (text "GND" (at 55.88 49.53 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
    (uuid d4a547ec-9472-4c3c-a502-f52fac596ab0)
  )
  (text "GPIO 2" (at 55.88 41.91 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
    (uuid d62acfcd-78e9-48e2-8cfb-4057b64de403)
  )

  (symbol (lib_id "Connector_Generic:Conn_01x05") (at 58.42 43.18 0) (mirror y) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 4b463a08-e787-4b7c-bdef-13b7b5a6299a)
    (property "Reference" "J1" (id 0) (at 58.42 31.75 0))
    (property "Value" "" (id 1) (at 58.42 34.29 0))
    (property "Footprint" "" (id 2) (at 58.42 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 58.42 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 56f4308e-ff30-42bb-9f09-b81ac503cb70))
    (pin "2" (uuid a5ca5cfb-2cb6-45b1-a7fd-6fc0023a2c7e))
    (pin "3" (uuid 79547c10-11c6-48a7-9e24-a6fcd8f65742))
    (pin "4" (uuid 0ca17e6c-1029-4889-9a83-6f0769cf8362))
    (pin "5" (uuid a735bf18-82ea-45cf-aa2a-6954bc33fa78))
  )

  (symbol (lib_id "Device:R_US") (at 67.31 41.91 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 74543e0c-18b0-49c1-b425-c9315fc34ed7)
    (property "Reference" "R1" (id 0) (at 69.85 40.6399 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "" (id 1) (at 69.85 43.1799 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 68.326 42.164 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 67.31 41.91 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 401187eb-99f9-4660-afbf-3b7061bbc6c1))
    (pin "2" (uuid fb1c9655-6c6f-4668-981a-2472c1f87067))
  )

  (symbol (lib_id "Sensor_Temperature:DS18B20") (at 87.63 43.18 0) (mirror y) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid a8154d9c-1b06-4e72-a2b3-462e2400566a)
    (property "Reference" "U1" (id 0) (at 93.98 41.9099 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "" (id 1) (at 93.98 44.4499 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (id 2) (at 113.03 49.53 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "http://datasheets.maximintegrated.com/en/ds/DS18B20.pdf" (id 3) (at 91.44 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 569ef19f-41c6-49ff-9222-120db80c369f))
    (pin "2" (uuid d4ad0b76-7b1f-4450-8204-db38fee6eca2))
    (pin "3" (uuid d92b7ab6-49b0-4642-ba09-1bd0e289155a))
  )

  (sheet_instances
    (path "/" (page "1"))
  )

  (symbol_instances
    (path "/4b463a08-e787-4b7c-bdef-13b7b5a6299a"
      (reference "J1") (unit 1) (value "Conn_01x05") (footprint "")
    )
    (path "/74543e0c-18b0-49c1-b425-c9315fc34ed7"
      (reference "R1") (unit 1) (value "4.7K") (footprint "")
    )
    (path "/a8154d9c-1b06-4e72-a2b3-462e2400566a"
      (reference "U1") (unit 1) (value "DS18B20") (footprint "Package_TO_SOT_THT:TO-92_Inline")
    )
  )
)
